import React, { useEffect } from "react";
import { NavigationContainer } from "@react-navigation/native";
import { StatusBar } from "expo-status-bar";
import MainNavigator from "./navigation/MainNavigator";
import { AuthProvider } from "./src/context/AuthProvider";

import * as Notifications from 'expo-notifications'; // ✅ Add this
import AsyncStorage from '@react-native-async-storage/async-storage'; // ✅ Add this

export default function App() {
 

  return (
    <>
      <StatusBar style="auto" />
      <AuthProvider>
        <MainNavigator />
      </AuthProvider>
    </>
  );
}
