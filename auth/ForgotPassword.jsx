import {
  View,
  Text,
  TextInput,
  StyleSheet,
  Pressable,
  Alert,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useContext, useState } from 'react';
import { apiRequest } from '../utils/api';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AuthContext } from '../src/context/AuthProvider';
import { logout } from '../utils/storage';
const ForgotPassword = () => {
  const navigation = useNavigation();
  const [phone, setPhone] = useState('');
  const [otpSent, setOtpSent] = useState(false);
  const [otp, setOtp] = useState('');

  const handleSendOtp = async () => {
        
    
    if (!phone) {
      Alert.alert('Error', 'Please enter your phone number.');
      return;
    }

    try {
      const response = await apiRequest('POST', '/api/users/auth/password-reset/request', {
        phone_no: phone,
      });

      Alert.alert('OTP Sent', response.data.message || 'Please check your phone.');
      setOtpSent(true);
    
    } catch (error) {
      console.error(error.response?.data || error.message);
      Alert.alert('Failed', error.response?.data?.message || 'Something went wrong');
    }
  };

  const handleVerifyOtp = async () => {
    if (!otp) {
      Alert.alert('Error', 'Please enter the OTP.');
      return;
    }

    try {
      const response = await apiRequest('POST', '/api/users/auth/password-reset/verify', {
        phone_no: phone,
        otp: otp,
      });
      console.log("RESPONSE : "+response);
      await AsyncStorage.setItem("accessToken",response.data.access_token);
      console.log(response.data)
      navigation.navigate('ResetPassword', { phone });// Go to reset screen
    } catch (error) {
      console.error(error.response?.data || error.message);
      Alert.alert('Verification Failed', error.response?.data?.message || 'Invalid OTP');
    }
  };

  return (
    <View style={styles.container}>
      <Image
        style={styles.image}
        source={require('../assets/logobgrm.png')}
      />
      <View style={{ paddingBottom: 20, alignItems: 'center' }}>
        <Text style={{ fontSize: 25, fontWeight: '300' }}>
          Forgot Password
        </Text>
      </View>

      <Text style={styles.label}>Phone Number</Text>
      <TextInput
        style={styles.input}
        onChangeText={setPhone}
        placeholder="Enter Phone Number"
        keyboardType="phone-pad"
        editable={!otpSent}
        value={phone}
      />

      {!otpSent ? (
        <Pressable onPress={handleSendOtp} style={styles.button}>
          <Text style={styles.buttonText}>Send OTP</Text>
        </Pressable>
      ) : (
        <>
          <Text style={styles.label}>Enter OTP</Text>
          <TextInput
            style={styles.input}
            onChangeText={setOtp}
            placeholder="Enter OTP"
            keyboardType="numeric"
            value={otp}
          />
          <Pressable onPress={handleVerifyOtp} style={styles.button}>
            <Text style={styles.buttonText}>Verify OTP</Text>
          </Pressable>
        </>
      )}

      <Pressable onPress={() => navigation.navigate('Login')}>
        <Text style={styles.backLogin}>Back to Login</Text>
      </Pressable>
    </View>
  );
};

export default ForgotPassword;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    paddingTop: 20,
    backgroundColor: '#fff',
  },
  image: {
    width: 250,
    height: 250,
    resizeMode: 'contain',
  },
  label: {
    fontSize: 15,
    color: '#555',
    marginBottom: 5,
  },
  input: {
    width: 300,
    padding: 15,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  button: {
    width: 300,
    height: 40,
    backgroundColor: '#000',
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 10,
  },
  buttonText: {
    color: '#fff',
    fontSize: 15,
    fontWeight: '500',
  },
  backLogin: {
    fontSize: 16,
    marginTop: 20,
    color: '#2196F3',
    textDecorationLine: 'underline',
  },
});
