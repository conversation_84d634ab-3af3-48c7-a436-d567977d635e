import {View , Text, Image, StyleSheet, TextInput, Pressable, Button, Alert} from 'react-native'
import { useNavigation } from '@react-navigation/native'
import { AuthContext } from "../src/context/AuthProvider";
import { useContext, useEffect, useState } from 'react';
import NetInfo from '@react-native-community/netinfo';
import { apiRequest } from '../utils/api';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getUserId,setUserId } from '../utils/storage';
import Loading from '../src/components/Loading';


const Login = () => {

    const [phone , setphone] = useState('');
    const [password , setpassword] = useState('');
    const navigation = useNavigation();
    const { login , logout } = useContext(AuthContext);
    const [loading , setLoading] = useState(false)
    const checkConnection = () => {
  NetInfo.fetch().then(state => {
    if (!state.isConnected) {
      Alert.alert(
        "No Internet",
        "Please turn on your mobile data or Wi-Fi.",
        [{ text: "OK" }]
      );
    } else {
      console.log("Connected via:", state.type); // e.g., 'cellular' or 'wifi'
    }
  });
};

useEffect(() => {
  checkConnection();
}, []);

const verifyuserdata = async (userid) =>{
  try{
    const res = await apiRequest('GET',`/api/users/${userid}`,null,logout)
    if(res.data.data.name){
      navigation.navigate('Userform');
    }
    else{
      login();
    }
  }catch(error){
    console.log("error",error.message)
  }
}

const handleLogin = async () => {
  if(phone && password !==''){
    try {
      await setLoading(true)
      const response = await apiRequest('POST','/api/users/auth/login', {
        phone_no: phone,
        password: password,
      },logout);
      const {access_token , refresh_token} = response.data.token;
      const {user_id} = response.data
      await AsyncStorage.setItem('accessToken', access_token);
      await AsyncStorage.setItem('refreshToken', refresh_token);
      await AsyncStorage.setItem('userId', user_id);
      await setUserId(user_id);
      verifyuserdata(user_id); 
      await setLoading(false)

    } catch (error) {
      console.error(error.response?.data || error.message);
      Alert.alert('Login Failed', error.response?.data?.message || 'Something went wrong');
    } 
    }
    else{
      Alert.alert("please enter phone number and password ")
    }
   
  };

  return (
   <View style={styles.container} >
    <Loading visible={loading} />
    <Image style={styles.image} source={require("../assets/logobgrm.png")} />
    
    <View  >

    <View style={{ paddingBottom: 20, alignItems: 'center' }}>
            <Text style={{ fontSize: 25, fontWeight: '300' }}>Login</Text>
    </View> 
    <Text style={styles.label}>Phone Number</Text>
    <TextInput style={styles.input} onChangeText={setphone} placeholder='Enter Phone Number' keyboardType="phone-pad" />
    <Text style={styles.label}>Password</Text>
    <TextInput style={styles.input} onChangeText={setpassword} placeholder='Enter Password' />
    
    <Pressable onPress={handleLogin} style={styles.button} >
        <Text style={styles.buttontext} >Login</Text>
    </Pressable>
    
    <Pressable onPress={()=>navigation.navigate('ForgotPassword')}>
    <Text style={{fontSize:18,marginTop:15,marginBottom:20,color:'rgba(255, 0, 0, 0.6)',fontWeight:100}} >Forgot Password</Text>
    </Pressable>

    <View style={{display:'flex',flexDirection:'row',gap:5,paddingTop:5,justifyContent:'center'}}>
      <Pressable onPress={()=>navigation.navigate('Signup')} ><Text 
      allowFontScaling={false}
          style={{fontSize:15, borderBottomWidth: 1, 
              borderBottomColor: 'black' }} >SignUp</Text></Pressable>
    </View>
    </View>
    
   </View>
  )
}

const styles = StyleSheet.create({
    container: {
        flex:1,
        alignItems:'center', 
        paddingTop:20,
        backgroundColor:'#fff'
    },
    image:{
        width: 250,
        height:250,
        resizeMode: "contain",
    },
    login:{
        fontSize:20,
        fontWeight:'700'
    },
    input:{
        width: 300,
        padding: 15,
        borderWidth: 1,
        borderColor: "#ccc",
        borderRadius: 5,
        marginBottom: 15,
        backgroundColor: "#fff",
    },
    label: {
        fontSize: 15,
        color: "#555",
        marginBottom: 5,
      },
    button:{
        width:"300",
        height:40,
        backgroundColor:'#000',
        borderRadius:5,
        alignItems:'center',
        justifyContent:'center'
    },
    buttontext:{
        color:'#fff',
        fontSize:15,
        fontWeight:'500'
    }
})

export default Login
