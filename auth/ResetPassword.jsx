import {
  View,
  Text,
  TextInput,
  StyleSheet,
  Pressable,
  Alert,
  Image,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useContext, useState } from 'react';
import { apiRequest } from '../utils/api';
import { AuthContext } from '../src/context/AuthProvider';

const ResetPassword = () => {
    const { logout } = useContext(AuthContext);
  
  const navigation = useNavigation();
  const route = useRoute();
  const { phone } = route.params;

  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const handleResetPassword = async () => {
    if (!password || !confirmPassword) {
      Alert.alert('Error', 'Please fill in both password fields.');
      return;
    }

    if (password !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match.');
      return;
    }

    try {
      const response = await apiRequest('POST', '/api/users/auth/password-reset/confirm', {
        newPassword: password,
      },logout);

      Alert.alert('Success', 'Your password has been reset.');
      navigation.navigate('Login');
    } catch (error) {
      console.error(error.response?.data || error.message);
      Alert.alert('Failed', error.response?.data?.message || 'Something went wrong');
    }
  };

  return (
    <View style={styles.container}>
      <Image
        style={styles.image}
        source={require('../assets/logobgrm.png')}
      />
      <View style={{ paddingBottom: 20, alignItems: 'center' }}>
        <Text style={{ fontSize: 25, fontWeight: '300' }}>
          Reset Password
        </Text>
      </View>

      <Text style={styles.label}>New Password</Text>
      <TextInput
        style={styles.input}
        onChangeText={setPassword}
        placeholder="Enter new password"
        secureTextEntry
      />

      <Text style={styles.label}>Confirm Password</Text>
      <TextInput
        style={styles.input}
        onChangeText={setConfirmPassword}
        placeholder="Confirm new password"
        secureTextEntry
      />

      <Pressable onPress={handleResetPassword} style={styles.button}>
        <Text style={styles.buttonText}>Reset Password</Text>
      </Pressable>

      <Pressable onPress={() => navigation.navigate('Login')}>
        <Text style={styles.backLogin}>Back to Login</Text>
      </Pressable>
    </View>
  );
};

export default ResetPassword;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    paddingTop: 20,
    backgroundColor: '#fff',
  },
  image: {
    width: 250,
    height: 250,
    resizeMode: 'contain',
  },
  label: {
    fontSize: 15,
    color: '#555',
    marginBottom: 5,
  },
  input: {
    width: 300,
    padding: 15,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  button: {
    width: 300,
    height: 40,
    backgroundColor: '#000',
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 10,
  },
  buttonText: {
    color: '#fff',
    fontSize: 15,
    fontWeight: '500',
  },
  backLogin: {
    fontSize: 16,
    marginTop: 20,
    color: '#2196F3',
    textDecorationLine: 'underline',
  },
});
