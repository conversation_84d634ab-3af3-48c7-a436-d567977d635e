  import { AntDesign } from '@expo/vector-icons';
  import React, { useState, useEffect } from 'react';
  import { View, TextInput, FlatList, Text, TouchableOpacity, StyleSheet, SafeAreaView, ScrollView, StatusBar, Platform, Pressable } from 'react-native';
  import { debounce } from 'lodash';
import { useNavigation } from '@react-navigation/native';
import {GEOAPIFY_API_KEY} from '@env'

  
  const SetLocation = ({ onLocationSelect }) => {
    const [query, setQuery] = useState('');
    const [suggestions, setSuggestions] = useState([]);
    const [loading, setLoading] = useState(false);
    const navigation = useNavigation();


    useEffect(() => {
      const fetchSuggestions = debounce(async () => {
        if (query.length < 3) return;
    
        try {
          setLoading(true);
          const response = await fetch(
            `https://api.geoapify.com/v1/geocode/autocomplete?text=${encodeURIComponent(query)}&limit=5&apiKey=${GEOAPIFY_API_KEY}`
          );
          const data = await response.json();
          setSuggestions(data.features || []);
          setLoading(false);
        } catch (error) {
          console.error('Error fetching location suggestions:', error);
        }
      }, 300); // waits 300ms after user stops typing
    
      fetchSuggestions();
      return () => fetchSuggestions.cancel(); // clean up
    }, [query]);

    const handleSelect = (item) => {
      const { formatted, lat, lon } = item.properties;
      onLocationSelect({ name: formatted, latitude: lat, longitude: lon });
      setQuery(formatted);
      setSuggestions([]);
    };

    return (
        <SafeAreaView style={{flex: 1,paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,backgroundColor:'#fff'}}>
          <StatusBar barStyle="dark-content" />
      <View style={styles.container}>
        <Text style={{fontWeight:'500',paddingBottom:10}} >Setlocation</Text>
        <View style={styles.search} >
          <AntDesign name="search1" size={20} color="black" />
        <TextInput
          placeholder="Search location..."
          value={query}
          onChangeText={setQuery}
        />
        </View>
        <View style={{display:'flex',flexDirection:'row',alignItems:'center',justifyContent:'center'}} ><Text style={{fontSize:12}} >Search for </Text><Text style={{fontWeight:'500'}} >Pincode </Text><Text style={{fontSize:12}}>for accurate location</Text></View>
        {loading && <Text style={{textAlign:'center',paddingTop:10}} >searching</Text>}
        {suggestions.length > 0 && (
          <FlatList
            data={suggestions}
            keyExtractor={(item) => item.properties.place_id}
            renderItem={({ item }) => (
              <TouchableOpacity onPress={() => handleSelect(item)} style={styles.suggestionItem}>
                <Text>{item.properties.formatted}</Text>
              </TouchableOpacity>
            )}
          />
        )}
        <Pressable onPress={()=>navigation.navigate('Userform')} ><Text>Next</Text></Pressable>
      </View>      
      </SafeAreaView>
    );
  };

  export default SetLocation;

  const styles = StyleSheet.create({
    container: {
      backgroundColor: '#fff',
      marginHorizontal:16,
      marginTop:10
    },
    input: {
      height: 50,
      borderColor: '#ccc',
      borderWidth: 1,
      paddingHorizontal: 12,
      borderRadius: 8,
    },
    suggestionItem: {
      padding: 12,
      borderBottomColor: '#eee',
      borderBottomWidth: 1,
    },
    search:{
      height:40,
      display:'flex',
      flexDirection:'row',
      alignItems:'center',
      justifyContent:'flex-start',
      borderRadius:8,
      paddingLeft:10,
      backgroundColor:'#e0e8f2',
      gap:10,
    }
  });
