import React, { useContext, useState } from 'react'
import { Alert, Image, Pressable, StyleSheet, Text, TextInput, View } from 'react-native'
import { useNavigation } from '@react-navigation/native'
import { apiRequest } from '../utils/api';
import { AuthContext } from '../src/context/AuthProvider';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Loading from '../src/components/Loading';




const Signup = () => {
    const navigation = useNavigation();
    const [phone_no, setPhone] = useState('');
    const [otp_no, set_otp_no] = useState('');
    const [otpSent, setOtpSent] = useState(false);
    const [is_otp_verified,set_is_otp_verified] = useState(false);
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const { logout , setPhoneNumber,setpassworduh ,login} = useContext(AuthContext);
    const [visible , setvisible] = useState(false)
    
    const sendOtp = async () => {
      if(password == ''){
        Alert.alert("password should not be empty")
      }
      if (password !== confirmPassword) {
      Alert.alert("password doesnt match")
    }
    else{
        try {
            if(phone_no == ''){
                Alert.alert('Please Enter Valid Phone No !')
            }
            else{
              await setvisible(true)
              const response = await apiRequest('POST','/api/users/auth/register', { phone_no : phone_no },logout);
              await setvisible(false)
              setOtpSent(true);// Show password fields
            } 
        } catch (error) {
          if(error.status == 400){
            Alert.alert("Phone number already found...")
          }
          console.error('Send OTP failed:', error.status);
        }
      }
    };

  const verifyAndRegister = async () => {
    if (password !== confirmPassword) {
      Alert.alert("password doesnt match")
    }
    try {
      const otp = otp_no; 
      await setvisible(true)
      const response = await apiRequest('POST','/api/users/auth/verify', {
        phone_no:phone_no,
        otp:otp,
        password:password,
      },logout);
      await setPhoneNumber(phone_no);
      await setpassworduh(password);
      const {user_id} = response.data;
      await AsyncStorage.setItem('accessToken', access_token);
      await AsyncStorage.setItem('refreshToken', refresh_token);
      await AsyncStorage.setItem('userId', user_id);
      login();
      await setvisible(false)
      // Or go to Home
    } catch (error) {
      console.error('Verify failed:', error.response?.data || error.message);
    }
  };

  return (
    <View style={styles.container} >
      <Loading visible={visible} />
       <Image style={styles.image} source={require("../assets/logobgrm.png")} />
       <View  >

       <View style={{ paddingBottom: 20, alignItems: 'center' }}>
        <Text style={{ fontSize: 25, fontWeight: '300' }}>Signup</Text>
      </View>

        { (!otpSent || is_otp_verified) &&(
          <>
          <Text style={styles.label}  >Phone Number</Text>
          <TextInput style={styles.input} value={phone_no} onChangeText={setPhone}
             placeholder='Enter Phone Number' 
             keyboardType="phone-pad" />
             
            <Text style={styles.label}>Password</Text>
            <TextInput
              style={styles.input}
              placeholder='Password'
              value={password}
              onChangeText={setPassword}
            />

            <Text style={styles.label}>Confirm Password</Text>
            <TextInput
              style={styles.input}
              placeholder='Confirm Password'
              secureTextEntry
              value={confirmPassword}
              onChangeText={setConfirmPassword}
            />
          
          </>
        )

        }
       

        {/* AFTER OTP SEND I AM SHOWING THIS */}

        {otpSent && (
          <>
            <Text style={styles.label}>Enter OTP</Text>
            <TextInput
              style={styles.input}
              value={otp_no}
              onChangeText={(text) => set_otp_no(text.slice(0, 6))} // Limit to 6 digits
              placeholder="Enter OTP"
              keyboardType="numeric"
              maxLength={6}
            />
          </>
        )}


        
        {/* BUTTONS */}
        <Pressable
          style={styles.button}
          onPress={otpSent ? verifyAndRegister : sendOtp}

        >
          <Text style={styles.buttontext}>{otpSent ? "Verify" : "Register"}</Text>
        </Pressable>
        {/* <View style={{ display: 'flex', flexDirection: 'row', gap: 5, marginTop: 15,alignContent:'center',justifyContent:'flex-start' }}>
          <Text style={{ fontSize: 15 }}>Already have an account</Text>
          <Pressable onPress={() => navigation.navigate('Login')}>
            <Text style={{ 
              fontSize: 16, 
              borderBottomWidth: 1, 
              borderBottomColor: 'black' 
            }}>
              Login
            </Text>
          </Pressable>
        </View> */}

        <View style={{ display:'flex',flexDirection:'row',gap:5,paddingTop:5,justifyContent:'center' }}>
  <Pressable onPress={() => navigation.navigate('Login')}>
    <Text allowFontScaling={false} style={{ 
      fontSize: 16, 
      borderBottomWidth: 1, 
      borderBottomColor: 'black',
      marginLeft: 5, 
    }}>
      Login
    </Text>
  </Pressable>
</View>


       </View>   
      </View>
  )
}

const styles = StyleSheet.create({
    container: {
        flex:1,
        alignItems:'center', 
        paddingTop:20,
        backgroundColor:'#fff'
    },
    image:{
        width: 250,
        height:250,
        resizeMode: "contain",
    },
    login:{
        fontSize:20,
        fontWeight:'700'
    },
    input:{
        width: 300,
        padding: 15,
        borderWidth: 1,
        borderColor: "#ccc",
        borderRadius: 5,
        marginBottom: 15,
        backgroundColor: "#fff",
    },
    label: {
        fontSize: 15,
        color: "#555",
        marginBottom: 5,
      },
    button:{
        width:"300",
        height:40,
        backgroundColor:'#000',
        borderRadius:5,
        alignItems:'center',
        justifyContent:'center'
    },
    buttontext:{
        color:'#fff',
        fontSize:15,
        fontWeight:'500'
    }
})


export default Signup
