import React, { useContext, useEffect, useRef, useState } from 'react'
import { Alert, FlatList, Platform, Pressable, SafeAreaView, StatusBar, StyleSheet, Text, TextInput, View } from 'react-native'
import { useNavigation } from '@react-navigation/native'
import { AntDesign } from '@expo/vector-icons';
import { debounce } from 'lodash';
import {GEOAPIFY_API_KEY} from '@env'
import { TouchableOpacity } from 'react-native';
import { apiRequest } from '../utils/api';
import * as Notifications from 'expo-notifications';
import { registerForPushNotificationsAsync } from '../utils/pushNotificationUtil';
import { AuthContext } from '../src/context/AuthProvider';
import { CustomMultiSelect } from '../src/components/CustomMultiSelect';


Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: false,
    shouldSetBadge: false,
  }),
});


 


const Userform = () => {
  const { logout } = useContext(AuthContext);
  const notificationListener = useRef();
  const responseListener = useRef();


   useEffect(() => {
    registerForPushNotificationsAsync().then(token => {
      const tokenId2 = token.replace('ExponentPushToken[', '').replace(']', '');
      setpushnotification(tokenId2)
      // Send this token to your backend for later use
    });

    notificationListener.current = Notifications.addNotificationReceivedListener(notification => {
      console.log('Notification Received:', notification);
    });

    responseListener.current = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('User Interacted with Notification:', response);
    });

    return () => {
      Notifications.removeNotificationSubscription(notificationListener.current);
      Notifications.removeNotificationSubscription(responseListener.current);
    };
  }, []);
  const [name , setname] = useState('');
  const [mailid,setmailid] = useState('');
  const [selectedItems, setSelectedItems] = useState([]);
  const [address, setAddress] = useState('');
  const [pushnotification , setpushnotification] = useState('')

  const navigation = useNavigation();
  const skillsList = [
  { id: '1', name: 'Electrician' },
  { id: '2', name: 'Plumber' },
  { id: '3', name: 'Carpenter' },
  { id: '4', name: 'Painter' },
];

 const [query, setQuery] = useState('');
     const [suggestions, setSuggestions] = useState([]);
     const [loading, setLoading] = useState(false);
     const [onLocationSelect , setonlocationselect] = useState({})

  const onSelectedItemsChange = (selected) => {
    setSelectedItems(selected);
    // You can also update parent form state here
  };

  const handlenext = async ()=>{
    if (name == ''){
      Alert.alert("enter name properly...")
    }
    if (address == ''){
      Alert.alert("enter addresss properly...")
    }
    if(selectedItems == []){
      Alert.alert("enter works properly...")
    }
    if(onLocationSelect == {}){
      Alert.alert("select location properly...")
    }
    if(pushnotification == ''){
      Alert.alert("kindly provide permission for send notification");
    }
    else{
      try {
             const res = await apiRequest('PUT','/api/users/',{
               email_id:mailid,
               pushnotification_id:pushnotification,
               role:'WORKER',
               name:name,
               location_lat:onLocationSelect.latitude,
               location_long:onLocationSelect.longitude,
               known_works:selectedItems,
               favourite_category:[]
             },logout)
             if(res.data.status){
                 Alert.alert("user updated successfully");
             }
             else{
                 Alert.alert("Kindly provide the details properly")
             }
           }catch(error){
             console.log('error',error)
           }  
    }
  }

  useEffect(() => {
        const fetchSuggestions = debounce(async () => {
          if (query.length < 3) return;
      
          try {
            setLoading(true);
            const response = await fetch(
              `https://api.geoapify.com/v1/geocode/autocomplete?text=${encodeURIComponent(query)}&limit=5&apiKey=${GEOAPIFY_API_KEY}`
            );
            const data = await response.json();
            setSuggestions(data.features || []);
            setLoading(false);
          } catch (error) {
            console.error('Error fetching location suggestions:', error);
          }
        }, 300); // waits 300ms after user stops typing
      
        fetchSuggestions();
        return () => fetchSuggestions.cancel(); // clean up
      }, [query]);

      const handleSelect = (item) => {
      const { formatted, lat, lon } = item.properties;
      setonlocationselect({ name: formatted, latitude: lat, longitude: lon });
      setQuery(formatted);
      setSuggestions([]);
    };

  return (
     <SafeAreaView style={{flex: 1,paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,backgroundColor:'#fff'}}>
    <StatusBar barStyle="dark-content" />
    <View style={styles.outercontainer} >
    <View style={{marginHorizontal:16,paddingTop:20}} >
        <Text style={{fontSize:18,fontWeight:'500'}} >Provide a few details before proceeding!!</Text>
        <Text style={styles.label} >Name</Text>
        <View style={styles.input} >
          <TextInput onChangeText={setname} style={{width:'100%',fontSize:13}} placeholder='Name' />
        </View>
        <Text style={styles.label} >Mail ID</Text>
        <View style={styles.input} >
          <TextInput onChangeText={setmailid} style={{width:'100%',fontSize:13}} placeholder='Mail ID' />
        </View>
        <View style={{zIndex:1000}} >
        <Text style={styles.label} >Known works</Text>
       {/*  <MultiSelect
        items={skillsList}
        uniqueKey="id"
        onSelectedItemsChange={onSelectedItemsChange}
        selectedItems={selectedItems}
        selectText="  Select known works"
        searchInputPlaceholderText="Search works..."
        tagRemoveIconColor="#e0e8f2"
        tagBorderColor="#e0e8f2"
        tagTextColor="#000"
        selectedItemTextColor="#000"
        selectedItemIconColor="#000"
        itemTextColor="#000"
        displayKey="name"
        searchInputStyle={{ color: '#000' }}
        submitButtonColor="#000"
        submitButtonText="Done"
        styleDropdownMenuSubsection={styles.dropdown}
        styleMainWrapper={styles.wrapper}
      /> */}

      
<CustomMultiSelect
  items={skillsList}
  onSelect={(selected) => console.log(selected)}
/>
      </View>
      <View style={styles.container}>
        <Text style={styles.label} >Setlocation</Text>
        <View style={styles.search} >
          <AntDesign name="search1" size={20} color="black" />
        <TextInput
          placeholder="Search pincode..."
          value={query}
          onChangeText={setQuery}
          keyboardType='numeric'
        />
        </View>
        <View style={{display:'flex',flexDirection:'row',alignItems:'center',justifyContent:'center'}} ><Text style={{fontSize:12}} >Search for </Text><Text style={{fontWeight:'500'}} >Pincode </Text><Text style={{fontSize:12}}>for accurate location</Text></View>
        {loading && <Text style={{textAlign:'center',paddingTop:10}} >searching</Text>}
        {suggestions.length > 0 && (
          <FlatList
            data={suggestions}
            keyExtractor={(item) => item.properties.place_id}
            renderItem={({ item }) => (
              <TouchableOpacity onPress={() => handleSelect(item)} style={styles.suggestionItem}>
                <Text>{item.properties.formatted}</Text>
              </TouchableOpacity>
            )}
          />
        )}
      </View>  
      <View>
         <Text style={styles.label}>Address</Text>
         <View style={{ height:70,
     borderColor:'#e0e8f2',
     borderWidth:1,
     borderRadius:8,
     justifyContent:'center',
     paddingHorizontal:10}} >
      <TextInput
        style={{width:'100%',fontSize:13,paddingVertical:8}}
        placeholder="Enter your full address"
        value={address}
        onChangeText={setAddress}
        multiline
        numberOfLines={4} // optional, for initial height
        textAlignVertical="top" // makes sure text starts from top
      />
      </View>
      </View>
    </View>
    
    <View style={{marginHorizontal:12}}>
      <Pressable onPress={()=>{navigation.navigate('Terms')}} >
      <Text style={{textAlign:'center',fontSize:16,fontWeight:'400'}} >Skip for now</Text>
    </Pressable>
      <Pressable onPress={handlenext} style={{height:45,width:'100%',backgroundColor:'#000',borderRadius:8,alignItems:'center',justifyContent:'center',marginVertical:12}} ><Text style={{color:'#fff',fontSize:16,fontWeight:600}} >Next</Text></Pressable>
    </View>
    </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  input:{
     height:42,
     borderColor:'#e0e8f2',
     borderWidth:1,
     borderRadius:8,
     justifyContent:'center',
     paddingHorizontal:10
    },
    label:{
      fontSize:13,
      paddingTop:10,
      paddingBottom:5,
      fontWeight:'500'
    },
    button:{
      width:'48%',
      alignItems:'center',
      backgroundColor: '#000',
      paddingVertical: 12,
      paddingHorizontal: 20,
      borderRadius: 8
    },
     dropdown: {
    borderWidth: 1.5,
    borderColor: '#e0e8f2',
    borderRadius: 8, // Rounded corners
    paddingHorizontal: 10,
    backgroundColor: '#fff',
  },
  wrapper: {
    borderRadius: 8, // Optional, outer wrapper
  },
  outercontainer:{
    height:'100%',
    display:'flex',
    flexDirection:'column',
    justifyContent:'space-between'
  },
  container: {
      backgroundColor: '#fff',

    },
    
    suggestionItem: {
      padding: 12,
      borderBottomColor: '#eee',
      borderBottomWidth: 1,
    },
    search:{
      height:40,
      display:'flex',
      flexDirection:'row',
      alignItems:'center',
      justifyContent:'flex-start',
      borderRadius:8,
      paddingLeft:10,
      backgroundColor:'#fff',
      gap:10,
      borderColor:'#e0e8f2',
     borderWidth:1,
    }
})

export default Userform

{/* <Pressable onPress={()=>navigation.navigate('Terms')} ><Text>next</Text></Pressable> */}