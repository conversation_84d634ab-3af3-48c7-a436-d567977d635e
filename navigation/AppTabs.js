import { StatusBar } from "expo-status-bar";
import React, { useEffect, useState } from "react";
import { Keyboard } from "react-native";
import { StyleSheet } from "react-native";
import { NavigationContainer } from "@react-navigation/native";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { createNativeStackNavigator } from "@react-navigation/native-stack";

import {
  Ionicons,
  AntDesign,
  MaterialCommunityIcons,
  Foundation,
} from "@expo/vector-icons";
import Home from "../src/pages/Home";
// import Paybefore from "../src/pages/Paybefore";
import Search from "../src/pages/Search";
import Notifications from "../src/pages/Notifications";
import Post from "../src/pages/CreatePostByUser";
import Profile from "../src/pages/Profile";
import History from '../src/pages/History'
import Profileterms from "../src/pages/Profileterms";
import Privacypolicy from "../src/pages/Privacypolicy";
import { UserFormProvider } from "../src/context/UserFormContext";
import PostsShowByCategory from "../src/pages/PostsShowByCategory"
import ShowPostDetails from "../src/pages/PostDetailScreen";
import Userform from "../auth/Userform";
import Updateuser from "../src/pages/Updateuser";



const Tab = createBottomTabNavigator();
const Stack = createNativeStackNavigator();

// ✅ Stack Navigator for Home + Dynamic Category Details
const HomeStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="HomeMain" component={Home} />
      <Stack.Screen name="PostsShowByCategory" component={PostsShowByCategory} />
      <Stack.Screen name="ShowPostDetails" component={ShowPostDetails} />
      <Stack.Screen name="Search" component={Search} />
      <Stack.Screen name="Notifications" component={Notifications} />
      <Stack.Screen name="updateuser" component={Updateuser} />
      <Stack.Screen name="Profile" component={Profile} />
    </Stack.Navigator>
  );
};

const Notifistack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Notifications" component={Notifications} />
      {/* <Stack.Screen name="Paybefore" component={Paybefore} /> */}
      <Stack.Screen name="Search" component={Search} />
    </Stack.Navigator>
  );
};
const Poststack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Post" component={Post} />
      <Stack.Screen name="Search" component={Search} />
      <Stack.Screen name="Notifications" component={Notifications} />
      <Stack.Screen name="Home" component={Home} />
    </Stack.Navigator>
  );
};
const Historystack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="History" component={History} />
      <Stack.Screen name="Search" component={Search} />
      <Stack.Screen name="Notifications" component={Notifications} />
      <Stack.Screen name="ShowPostDetails" component={ShowPostDetails} />
    </Stack.Navigator>
  );
};

const Profilestack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Profile" component={Profile} />
      <Stack.Screen name="Search" component={Search} />
      <Stack.Screen name="Notifications" component={Notifications} />
      <Stack.Screen name="Home" component={Home} />
      <Stack.Screen name="Terms" component={Profileterms} />
      <Stack.Screen name="Privacy" component={Privacypolicy} />
      <Stack.Screen name="updateuser" component={Updateuser} />
    </Stack.Navigator>
  );
};

// ✅ Main App with Keyboard Handling
export default function AppTabs() {
  const [keyboardVisible, setKeyboardVisible] = useState(false);

  useEffect(() => {
    const showSubscription = Keyboard.addListener("keyboardDidShow", () => {
      setKeyboardVisible(true);
    });

    const hideSubscription = Keyboard.addListener("keyboardDidHide", () => {
      setKeyboardVisible(false);
    });

    return () => {
      showSubscription.remove();
      hideSubscription.remove();
    };
  }, []);

  return (
    <>
    <StatusBar style="auto" />
      <Tab.Navigator
        screenOptions={({ route }) => ({
          headerShown: false,
          animation: "fade",
          lazy: false,
          unmountOnBlur: true,
          tabBarVisibilityAnimationConfig: {
            show: { animation: "none" },
            hide: { animation: "none" },
          },
          tabBarStyle: {
            display: keyboardVisible ? "none" : "flex",  // 👈 Hide tab bar when keyboard is open
            height: 65,
            paddingBottom: 10,
            paddingTop: 5,
            backgroundColor: "#ffffff",
            borderTopWidth: 0,
            ...styles.shadow,
          },
          tabBarIcon: ({ focused, color, size }) => {
            let iconName;

            switch (route.name) {
              case "HomeStack":
                iconName = focused ? "home" : "home";
                return <Foundation name={iconName} size={size} color={color} />;
              case "Notifymain":
                iconName = focused ? "notifications" : "notifications-outline";
                return <Ionicons name={iconName} size={size} color={color} />;
              case "Postmain":
                return (
                  <AntDesign
                    name="pluscircleo"
                    size={28}
                    color={focused ? "#141414" : "#e0e8f2"}
                  />
                );
              case "Historymain":
                iconName = focused ? "card-text" : "card-text-outline";
                return (
                  <MaterialCommunityIcons
                    name={iconName}
                    size={size}
                    color={color}
                  />
                );
              case "Profilemain":
                iconName = focused ? "person" : "person-outline";
                return <Ionicons name={iconName} size={size} color={color} />;
            }
          },
          tabBarActiveTintColor: "#141414",
          tabBarInactiveTintColor: "#e0e8f2",
          tabBarLabelStyle: { fontSize: 12 },
        })}
      >
        <Tab.Screen
          name="HomeStack"
          component={HomeStack}
          options={{ tabBarLabel: "Home" }}
        />
        <Tab.Screen name="Notifymain" component={Notifistack} options={{ tabBarLabel: "Notifications" }} />
        <Tab.Screen
          name="Postmain"
          component={Poststack}
          options={{ tabBarLabel: "" }}
        />
        <Tab.Screen name="Historymain" component={Historystack} options={{ tabBarLabel: "History" }} />
        <Tab.Screen name="Profilemain" component={Profilestack} options={{ tabBarLabel: "Profile" }} />
      </Tab.Navigator>
      </>
  );
}

const styles = StyleSheet.create({
  shadow: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.5,
    shadowRadius: 10,
    elevation: 10,
  },
});
