import React from "react";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import Login from '../auth/Login'
import SetLocation from "../auth/SetLocation";
import TermsAndConditions from "../auth/TermsAndConditions";
import Userform from "../auth/Userform";
import Signup from "../auth/Signup";
import ForgotPassword from "../auth/ForgotPassword";
import ResetPassword from "../auth/ResetPassword";
import { UserFormProvider } from "../src/context/UserFormContext";


const Stack = createNativeStackNavigator();

const AuthStack = () => {
  return (
    
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Signup" component={Signup} />
      <Stack.Screen name="Userform" component={Userform} />
      <Stack.Screen name="Login" component={Login} />
      <Stack.Screen name="Terms" component={TermsAndConditions} />
      <Stack.Screen name="ForgotPassword" component={ForgotPassword} />
      <Stack.Screen name="ResetPassword" component={ResetPassword} />
    </Stack.Navigator>
  );
};

export default AuthStack;
