import React, { useEffect, useState,useContext,useRef } from "react";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import AuthStack from "./AuthStack";
import AppTabs from "./AppTabs";
import { Image } from "react-native";
import { NavigationContainer } from "@react-navigation/native";
import { AuthContext } from "../src/context/AuthProvider";
import * as Notifications from 'expo-notifications';
import { registerForPushNotificationsAsync } from "../utils/pushNotificationUtil";
import { UserFormProvider } from "../src/context/UserFormContext";


const Stack = createNativeStackNavigator();
 /* Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: false,
    shouldSetBadge: false,
  }),
});  */

const MainNavigator = () => {
  const { isLoggedIn } = useContext(AuthContext);
  
   /* const notificationListener = useRef();
  const responseListener = useRef();  */

   /* useEffect(() => {
    registerForPushNotificationsAsync().then(token => {
       console.log('Expo Push Token:', token); 
    });

    notificationListener.current = Notifications.addNotificationReceivedListener(notification => {
      console.log('Notification Received:', notification);
    });

    responseListener.current = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('User Interacted with Notification:', response);
    });

    return () => {
      Notifications.removeNotificationSubscription(notificationListener.current);
      Notifications.removeNotificationSubscription(responseListener.current);
    };
  }, []); 
 */
  if (isLoggedIn === null) {
    return <>
    <Image source={require('../assets/logobgrm.png')} />
    </>; 
  }
  return (
    <UserFormProvider>
    <NavigationContainer screenOptions={{ headerShown: false }}>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
      {isLoggedIn ? (
        <Stack.Screen name="AppTabs" component={AppTabs} />
      ) : (
        <Stack.Screen name="AuthStack" component={AuthStack} />
      )}
      </Stack.Navigator>
    </NavigationContainer>
    </UserFormProvider>
  );
};

export default MainNavigator;
