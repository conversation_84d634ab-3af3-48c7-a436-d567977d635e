{"name": "daycents", "main": "node_modules/expo/AppEntry.js", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "postinstall": "patch-package"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@closeio/use-abortable-effect": "^1.0.0", "@config-plugins/react-native-blob-util": "^8.0.0", "@expo/config-plugins": "~8.0.0", "@expo/vector-icons": "^14.0.3", "@gorhom/bottom-sheet": "^4.6.4", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.0.1", "@react-native-community/netinfo": "11.3.1", "@react-native-masked-view/masked-view": "0.3.1", "@react-native-picker/picker": "2.7.5", "@react-navigation/drawer": "^6.7.2", "@react-navigation/native": "^6.0.2", "@react-navigation/stack": "^6.4.1", "@tanstack/react-query": "^5.56.2", "axios": "1.9.0", "crypto-es": "^2.1.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "expo": "^51.0.37", "expo-application": "~5.9.1", "expo-build-properties": "~0.12.5", "expo-constants": "~17.0.0", "expo-dev-client": "~4.0.29", "expo-device": "~6.0.2", "expo-font": "~12.0.9", "expo-image": "~1.13.0", "expo-image-manipulator": "~12.0.5", "expo-image-picker": "~15.1.0", "expo-linking": "~6.3.1", "expo-location": "~17.0.1", "expo-notifications": "~0.28.19", "expo-router": "~3.5.23", "expo-secure-store": "~13.0.2", "expo-splash-screen": "~0.27.5", "expo-status-bar": "~1.12.1", "expo-system-ui": "~3.0.7", "expo-web-browser": "~13.0.3", "lodash.isempty": "^4.4.0", "react": "18.2.0", "react-content-loader": "^7.0.2", "react-dom": "18.2.0", "react-native": "0.74.5", "react-native-blob-util": "^0.19.11", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "~2.16.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-modal-datetime-picker": "^18.0.0", "react-native-pager-view": "6.3.0", "react-native-reanimated": "~3.10.1", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-tab-view": "^3.5.2", "react-native-web": "~0.19.10", "react-native-webview": "13.8.6", "react-navigation": "^5.0.0", "use-debounce": "^10.0.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/jest": "^29.5.12", "@types/react": "~18.2.45", "@types/react-test-renderer": "^18.0.7", "jest": "^29.2.1", "jest-expo": "~51.0.3", "patch-package": "^8.0.0", "react-test-renderer": "18.2.0", "typescript": "~5.3.3"}, "private": true}