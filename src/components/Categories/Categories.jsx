import React, { useContext, useEffect, useState } from 'react';
import { ActivityIndicator, Alert, Pressable, ScrollView, StyleSheet, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Categoryicon from './SingleCategory';
import { apiRequest } from '../../../utils/api';
import { AuthContext } from '../../context/AuthProvider';

const Categories = () => {
  const navigation = useNavigation();
  const { logout } = useContext(AuthContext);
  
  const [resdata, setresdata] = useState([]);
  const [loading, setLoading] = useState(true);
  const [current , setcurrent]= useState(1)
  const [total , settotal] = useState(0)
  // Fetch categories on mount
  useEffect(() => {
  const fetchAllCategories = async () => {
    let allData = [];
    let currentPage = 1;
    let totalPages = 1;

    try {
      while (currentPage <= totalPages) {
        const response = await apiRequest('GET', `/api/categories?page=${currentPage}`, null, logout);
        const data = response.data.data;
        totalPages = response.data.pagination.totalPages;
        currentPage = response.data.pagination.currentPage + 1;

        allData = [...allData, ...data]; // merge new data into final array
      }

      setresdata(allData);
    } catch (error) {
      console.error('Error fetching categories:', error);
      Alert.alert("Error", "Failed to load categories.");
    } finally {
      setLoading(false);
    }
  };

  fetchAllCategories();
}, []);


  // Split array into chunks of size 4
   const chunkArray = (arr, size) => {
    const chunked = [];
    for (let i = 0; i < arr.length; i += size) {
      chunked.push(arr.slice(i, i + size));
    }
    return chunked;
  };

  const rows = chunkArray(resdata, 4);
 
  return (
    <View style={styles.category}>
      <View style={styles.flexone}>
        <View style={styles.innerone}>
          <Text style={styles.title}>Categories</Text>
              </View>
      </View>

       {loading ? (
        <ActivityIndicator size="small" color="#000" style={{ marginTop: 20 }} />
      )  : resdata.length === 0 ? (
        <Text style={styles.emptyText}>No Categories to Show</Text>
      )  : (
        <View style={styles.rowsContainer}>
           {rows.map((row, index) => (
            <ScrollView
              key={index}
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.scrollContainer}
            >
              {row.map((item) => (
                <Pressable
                  key={item.id}
                  onPress={() => navigation.navigate('PostsShowByCategory', {
                    category_id: item.id,
                    category_name: item.name,
                  })}
                  style={styles.itemWrapper}
                >
                  <Categoryicon
                    id={item.id}
                    category={item.name.replace(/^"(.*)"$/, '$1')}
                    image={item.url}
                  />
                </Pressable>
              ))}
            </ScrollView>
          ))} 

        </View>
      )} 
    
    </View>
  );
};

const styles = StyleSheet.create({
  category: {
    marginVertical: 10,
    marginHorizontal: 20,
  },
  flexone: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  innerone: {
    flexDirection: 'row',
    alignItems: 'baseline',
    gap: 5,
  },
  title: {
    fontSize: 17,
    fontWeight: '600',
    color: '#000',
  },
  count: {
    fontSize: 12,
    fontWeight: '500',
    color: '#555',
  },
  emptyText: {
    fontSize: 15,
    fontWeight: '500',
    color: 'red',
    alignSelf: 'center',
    marginTop: 20,
  },
  rowsContainer: {
    marginTop: 10,
  },
  scrollContainer: {
    marginBottom: 10,
  },
  itemWrapper: {
    marginRight: 15,
  },
});

export default Categories;
