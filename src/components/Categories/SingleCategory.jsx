import React from 'react';
import { View, Text,  StyleSheet,Image } from 'react-native';

const Categoryicon = ({ id, category, image }) => {
  return (
    <View style={styles.container}>
      <Image
        style={styles.image} 
        source={{uri:image}}
        contentFit="cover"
        transition={300}
      />
      <Text style={styles.text}>{category}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: 88,
    height: 120,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 10
  },
  image: {
    width: 70,
    height: 70,
    borderRadius:10,
    borderColor:'#E2E8F0',
    borderWidth:1
  },
  text: {
    textAlign: 'center',
    fontSize: 14,
    fontWeight: '500'
  }
});

export default Categoryicon;
