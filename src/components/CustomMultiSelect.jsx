import React, { useState } from 'react';
import {
  View, Text, TextInput, TouchableOpacity, StyleSheet, ScrollView
} from 'react-native';

export const CustomMultiSelect = ({ items, onSelect }) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedItems, setSelectedItems] = useState([]);

  const toggleDropdown = () => setIsDropdownOpen(!isDropdownOpen);

  const filteredItems = items.filter(item =>
    item.name.toLowerCase().includes(searchText.toLowerCase())
  );

  const toggleItem = (item) => {
    const isSelected = selectedItems.some(i => i.id === item.id);
    const updatedList = isSelected
      ? selectedItems.filter(i => i.id !== item.id)
      : [...selectedItems, item];
    setSelectedItems(updatedList);
    onSelect(updatedList);
  };

  const removeItem = (id) => {
    const updatedList = selectedItems.filter(i => i.id !== id);
    setSelectedItems(updatedList);
    onSelect(updatedList);
  };

  return (
    <View style={styles.container}>
      {/* Trigger button */}
      <TouchableOpacity onPress={toggleDropdown} style={styles.selectBox}>
        <Text style={styles.selectText}>Select known works</Text>
      </TouchableOpacity>

      {/* Selected tags */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tagsContainer}>
        {selectedItems.map(item => (
          <View key={item.id} style={styles.tag}>
            <Text style={styles.tagText}>{item.name}</Text>
            <TouchableOpacity onPress={() => removeItem(item.id)}>
              <Text style={styles.remove}>✕</Text>
            </TouchableOpacity>
          </View>
        ))}
      </ScrollView>

      {/* Dropdown */}
      {isDropdownOpen && (
        <View style={styles.dropdown}>
           <TextInput
            placeholder="Search works..."
            value={searchText}
            onChangeText={setSearchText}
            style={styles.searchInput}
            placeholderTextColor="#888"
          /> 
          <ScrollView style={{ maxHeight: 150 }}>
            {filteredItems.map(item => {
              const isSelected = selectedItems.some(i => i.id === item.id);
              return (
                <TouchableOpacity
                  key={item.id}
                  onPress={() => toggleItem(item)}
                  style={[styles.item, isSelected && styles.itemSelected]}
                >
                  <Text style={{ color: '#000' }}>{item.name}</Text>
                </TouchableOpacity>
              );
            })}
          </ScrollView>

          <TouchableOpacity onPress={toggleDropdown} style={styles.doneButton}>
            <Text style={{ color: '#fff' }}>Done</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 0,
  },
  selectBox: {
    borderWidth: 1,
    borderColor: '#e0e8f2',
    padding: 10,
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  selectText: {
    color: '#000',
  },
  tagsContainer: {
    flexDirection: 'row',
    marginVertical: 10,
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e0e8f2',
    paddingHorizontal: 10,
    paddingVertical: 5,
    marginRight: 5,
    borderRadius: 20,
  },
  tagText: {
    color: '#000',
    marginRight: 5,
  },
  remove: {
    color: '#000',
    fontWeight: 'bold',
  },
  dropdown: {
    borderWidth: 1,
    borderColor: '#ccc',
    padding: 10,
    borderRadius: 8,
    backgroundColor: '#fff',
    marginTop: 5,
  },
  searchInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 8,
    borderRadius: 6,
    marginBottom: 10,
    color: '#000',
  },
  item: {
    padding: 10,
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
  },
  itemSelected: {
    backgroundColor: '#e0e8f2',
  },
  doneButton: {
    backgroundColor: '#000',
    padding: 10,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 10,
  },
});
