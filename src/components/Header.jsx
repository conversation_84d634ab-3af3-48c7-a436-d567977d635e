import React from 'react'
import { Image, Pressable, StyleSheet, Text, View } from 'react-native'
import AntDesign from '@expo/vector-icons/AntDesign';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { useNavigation } from "@react-navigation/native";

const Header = () => {
    const navigation = useNavigation();
  return (
    <View style={styles.header} >

        <View style={styles.flexone} >
            <Image style={{width:90,height:90}} source={require('../../assets/logobgrm.png')} />
        </View>
        
        <View style={styles.flextwo} >
            <Pressable onPress={()=>navigation.navigate('Notifications')} style={styles.iconout} ><MaterialCommunityIcons name="bell-ring-outline" size={24} color="#0F172A" /></Pressable>
        </View>
    </View>
  )
}

const styles = StyleSheet.create({
    header:{
        display:'flex',
        flexDirection:'row',
        alignItems:'center',
        justifyContent:'space-between',
        paddingRight:20
    },
    flexone:{
        
    },
    flextwo:{
        display:'flex',
        flexDirection:'row',
        justifyContent:'space-between',
        gap:10
    },
    iconout:{
        width:45,
        height:45,
        display:'flex',
        flexDirection:'row',
        alignItems:'center',
        justifyContent:'center',
        borderRadius:8,
        borderWidth:1.5,
        borderColor:'#E2E8F0'
    },
})

export default Header
