import React, { useContext, useState } from 'react';
import {
  View,
  Text,
  Pressable,
  StyleSheet,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import ViewPostModal from './ViewPostModal';
import { apiRequest } from '../../../utils/api';
import { AuthContext } from '../../context/AuthProvider';
import { Alert } from 'react-native';
import Postrequests from './Postrequests';

const PostHistory = ({ icon, status, color1, color2, postDetails }) => {
  const [viewVisible, setViewVisible] = useState(false);
  const [updateVisible, setUpdateVisible] = useState(false);
  const {logout} = useContext(AuthContext)
  const [viewreq , setviewreq] = useState(false)

const deletePost = async (id) => {
  if (!id) {
    console.log("Provide post ID");
    return false;
  }

  try {
    const res = await apiRequest('DELETE', `//api/posts/${id}`, {}, logout);
    console.log('Post deleted:', res.data);
    Alert.alert("post deleted succesfully...")
    return true;
  } catch (error) {
    console.error('Failed to delete post:', error.message);
    Alert.alert('Delete Failed', 'Could not delete the post. Please try again.');
    return false;
  }
};

const handleDelete = () => {
  Alert.alert(
    'Confirm Delete',
    'Are you sure you want to delete this post?',
    [
      {
        text: 'Cancel',
        onPress: () => console.log('Delete cancelled'),
        style: 'cancel',
      },
      {
        text: 'Delete',
        style: 'destructive',
        onPress: async () => {
          const success = await deletePost(postDetails.id);
          if (success) {
            // Optional: update UI, navigate back, or show success toast
            console.log('Post deleted successfully');
            // Example: navigation.goBack(); or setPosts(...) if in a list
          }
        },
      },
    ],
    { cancelable: true }
  );
};



  return (
    <>
      <View style={styles.container}>
        <View style={styles.leftSection}>
          <MaterialIcons name={icon} size={30} color="black" />
          <View style={styles.infoBox}>
            <Text style={styles.title}>{postDetails.title}</Text>

            <Text style={[styles.statusText, { backgroundColor: color1, color: color2 }]}>
              {postDetails.status}
            </Text>

            <View style={styles.buttonRow}>
              <Pressable onPress={() => setViewVisible(true)} style={[styles.button, styles.viewBtn]}>
                <Text style={[styles.buttonText, styles.viewText]}>View</Text>
              </Pressable>

              {/* <Pressable onPress={() => setUpdateVisible(true)} style={[styles.button, styles.updateBtn]}>
                <Text style={[styles.buttonText, styles.updateText]}>Update</Text>
              </Pressable> */}

              <Pressable onPress={handleDelete} style={[styles.button, styles.deleteBtn]}>
                <Text >Delete</Text>
              </Pressable>
              <Pressable onPress={()=>setviewreq(true)} style={[styles.button, styles.deleteBtn]}>
                <Text >Request</Text>
              </Pressable>
            </View>
          </View>
        </View>

        <Text style={styles.date}>
          {new Date(postDetails.job_date).toLocaleDateString('en-IN') || '--.--'}
        </Text>
      </View>

      {/* Modals */}
      <ViewPostModal
        visible={viewVisible}
        onClose={() => setViewVisible(false)}
        post={postDetails}
      />
      <Postrequests
      visible={viewreq}
      onClose={()=>setviewreq(false)}
      id={postDetails.id}
      />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 6,
    marginHorizontal: 12,
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  leftSection: {
    flexDirection: 'row',
    gap: 12,
    flex: 1,
  },
  infoBox: {
    flex: 1,
    gap: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  statusText: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 6,
    fontWeight: '500',
    fontSize: 12,
    alignSelf: 'flex-start',
  },
  date: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6D6D72',
    marginTop: 4,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    flexWrap: 'wrap',
    gap: 10,
    marginTop: 8,
  },
  button: {
    marginLeft:2,
    paddingVertical: 6,
    paddingHorizontal: 15,
    borderRadius: 20,
    borderWidth: 1.5,
    backgroundColor: '#fff',
  },
  buttonText: {
    fontSize: 13,
    fontWeight: '600',
  },
  viewBtn: {
    borderColor: '#007AFF',
  },
  viewText: {
    color: '#007AFF',
  },
  updateBtn: {
    borderColor: '#34C759',
  },
  updateText: {
    color: '#34C759',
  },
  deleteBtn: {
    borderColor: '#FF3B30',
  },
  deleteText: {
    color: '#FF3B30',
  },
});

export default PostHistory;
