import React, { useContext, useEffect, useState } from 'react';
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Pressable,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { apiRequest } from '../../../utils/api';
import { AuthContext } from '../../context/AuthProvider';

const Postrequests = ({ visible, onClose, id  }) => {
    const {logout} = useContext(AuthContext);
    const [data,setdata] = useState([]);
    const [workers, setWorkers] = useState([]);

    const acceptrequest = async (workerid) =>{
      if(workerid){
        try{
          const res = await apiRequest('POST','/api/works/assign',{
            post_id:id,
            worker_id:workerid
          },logout)

          console.log(res.data)
        }catch(error){
          if(error.status == 409){
            Alert.alert("youn already accepted the worker for this post")
          }
          console.log("error in accept req component",error)
        }
      }
    }

    /* useEffect(()=>{
        const fetchreq = async(postid)=>{
            try{
                const res = await apiRequest('GET',`/api/posts/${postid}/requests`,null,logout)
                console.log(res.data)
            }catch(error){
                console.log("error in fetching post requests",error);
            }
        }
        fetchreq(id);
    },[]) */
    

const fetchreq = async (postid) => {
  try {
    const res = await apiRequest('GET', `/api/posts/${postid}/requests`, null, logout);
    
    await setdata(res.data.data); // This is okay to update state
    const requestList = res.data.data.users || [];
    
    const fetchedWorkers = [];

    for (const req of requestList) {
      if (req.worker_id) {
        try {
          const workerRes = await apiRequest('GET', `/api/users/${req.worker_id}`, null, logout);
          await setWorkers(workerRes.data.data)
          console.log(workerRes.data.data)
        } catch (err) {
          console.log(`Error fetching worker ${req.worker_id}:`, err.message);
        }
      }
    }

  } catch (error) {
    console.log("Error in fetching post requests:", error.message);
  }
};


      useEffect(() => {
        if (visible && id) {
        fetchreq(id);
  }
}, [visible, id]);


  if (!id) return null;

  return (
    <Modal visible={visible} animationType="slide" transparent>
      <View style={styles.overlay}>
        <View style={styles.modal}>
          {/* <Text style={styles.header}>Post Details</Text>

          <ScrollView contentContainerStyle={styles.content}>
            <InfoRow icon="title" label="Title" value={post.title} />
            <InfoRow icon="description" label="Description" value={post.description} />
            <InfoRow icon="phone" label="Mobile No" value={post.mobile_no} />
            <InfoRow icon="place" label="Pincode" value={post.pincode} />
            <InfoRow icon="attach-money" label="Amount" value={`₹${post.amount}`} />
            <InfoRow icon="event" label="Job Date" value={post.job_date} />
            <InfoRow icon="location-on" label="Location" value={post.location_name || 'N/A'} />
          </ScrollView>*/}
          <Text>No of requests : {data.count}</Text>
          <ScrollView style={{ maxHeight: 300 }}>
  {workers.length === 0 ? (
    <Text>No worker details found</Text>
  ) : (
    workers.map((worker, index) => (
      <View key={index} style={{ marginBottom: 12, padding: 10, borderWidth: 1, borderColor: '#ccc', borderRadius: 8 }}>
        <Text style={{ fontWeight: 'bold' }}>{worker.name || 'Unnamed Worker'}</Text>
        <Text>Email: {worker.email_id || 'N/A'}</Text>
        <Text >Known works</Text>
        {
            worker.known_works.map((item)=>(
                <Text  key={item}>{item}</Text>
            ))
        }
        <Text>Location : NearYou</Text>
        <View style={{display:'flex',flexDirection:'row',alignItems:'center',justifyContent:'space-between',marginTop:15}} >
            <Pressable onPress={()=>acceptrequest(worker.id)} style={{height:30,width:'48%',backgroundColor:'#000',alignItems:'center',justifyContent:'center',borderRadius:8}} ><Text style={{color:'#fff'}} >Accept</Text></Pressable>
            <Pressable style={{height:30,width:'48%',backgroundColor:'#fff',borderColor:'#e0e8f2',borderRadius:8,borderWidth:1.5,alignItems:'center',justifyContent:'center'}}><Text style={{color:'red'}} >Decline</Text></Pressable>
        </View>
        {/* Add more fields if needed */}
      </View>
    ))
  )}
</ScrollView>

          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>Close</Text>
          </TouchableOpacity> 
        </View>
      </View>
    </Modal>
  );
};


const InfoRow = ({ icon, label, value }) => (
  <View style={styles.row}>
    <MaterialIcons name={icon} size={20} color="#3A86FF" style={styles.icon} />
    <Text style={styles.label}>{label}:</Text>
    <Text style={styles.value}>{value || '-'}</Text>
  </View>
);


const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: '#00000055',
    justifyContent: 'center',
    padding: 20,
  },
  modal: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    maxHeight: '85%',
    elevation: 6,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  header: {
    fontSize: 20,
    fontWeight: '700',
    color: '#3A86FF',
    marginBottom: 16,
    textAlign: 'center',
  },
  content: {
    paddingBottom: 10,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 14,
    flexWrap: 'wrap',
  },
  icon: {
    marginTop: 2,
    width: 24,
  },
  label: {
    fontWeight: '600',
    color: '#333',
    marginLeft: 8,
    width: 90,
  },
  value: {
    flex: 1,
    color: '#444',
    fontSize: 14,
  },
  closeButton: {
    marginTop: 18,
    backgroundColor: '#3A86FF',
    paddingVertical: 12,
    borderRadius: 8,
  },
  closeButtonText: {
    color: '#FFFFFF',
    textAlign: 'center',
    fontWeight: '600',
    fontSize: 15,
  },
});

export default Postrequests;
