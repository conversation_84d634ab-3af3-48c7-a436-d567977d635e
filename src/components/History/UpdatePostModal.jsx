import React, { useState, useEffect } from 'react';
import {
  Modal, View, Text, StyleSheet, TextInput, TouchableOpacity, ScrollView, Platform
} from 'react-native';
import { MaterialIcons, Ionicons, FontAwesome } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Picker } from '@react-native-picker/picker';
import { GEOAPIFY_API_KEY } from '@env';

const UpdatePostModal = ({ visible, onClose, post, onUpdate, categories = [] }) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [mobileNo, setMobileNo] = useState('');
  const [pincode, setPincode] = useState('');
  const [amount, setAmount] = useState('');
  const [jobDate, setJobDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [location, setLocation] = useState('');
  const [categoryId, setCategoryId] = useState('');

  useEffect(() => {
    if (post) {
      setTitle(post.title || '');
      setDescription(post.description || '');
      setMobileNo(post.mobile_no || '');
      setPincode(post.pincode || '');
      setAmount(post.amount?.toString() || '');
      setJobDate(post.job_date ? new Date(post.job_date) : new Date());
      setLocation(post.location_name || '');
      setCategoryId(post.category_id || '');
    }
  }, [post]);

  useEffect(() => {
    if (pincode.length === 6) fetchLocationName(pincode);
  }, [pincode]);

  const fetchLocationName = async (pin) => {
    try {
      const response = await fetch(`https://api.geoapify.com/v1/geocode/search?postcode=${pin}&apiKey=${GEOAPIFY_API_KEY}`);
      const data = await response.json();
      if (data.features?.length > 0) {
        setLocation(data.features[0].properties.formatted);
      }
    } catch (err) {
      console.error("Failed to fetch location:", err);
    }
  };

  const handleDateChange = (event, selectedDate) => {
    if (selectedDate) setJobDate(selectedDate);
    setShowDatePicker(false);
  };

  const handleUpdate = () => {
    const updatedPost = {
      ...post,
      title,
      description,
      mobile_no: mobileNo,
      pincode,
      amount,
      job_date: jobDate.toISOString(),
      location_name: location,
      category_id: categoryId,
    };
    if (onUpdate) onUpdate(updatedPost);
    onClose();
  };

  if (!post) return null;

  return (
    <Modal visible={visible} animationType="slide" transparent>
      <View style={styles.overlay}>
        <View style={styles.modalCard}>
          <Text style={styles.header}>Update Post</Text>
          <ScrollView showsVerticalScrollIndicator={false}>
            <FormField icon={<MaterialIcons name="title" size={20} color="#444" />} placeholder="Title" value={title} onChangeText={setTitle} />
            <FormField icon={<MaterialIcons name="description" size={20} color="#444" />} placeholder="Description" value={description} onChangeText={setDescription} multiline />
            <FormField icon={<Ionicons name="call" size={20} color="#444" />} placeholder="Mobile No" value={mobileNo} onChangeText={setMobileNo} keyboardType="phone-pad" />
            <FormField icon={<MaterialIcons name="place" size={20} color="#444" />} placeholder="Pincode" value={pincode} onChangeText={setPincode} keyboardType="numeric" />
            {location ? <Text style={styles.locationText}>{location}</Text> : null}
            <FormField icon={<FontAwesome name="rupee" size={18} color="#444" />} placeholder="Amount" value={amount} onChangeText={setAmount} keyboardType="numeric" />

            {/* Date Picker */}
            <TouchableOpacity onPress={() => setShowDatePicker(true)} style={styles.dateField}>
              <MaterialIcons name="event" size={20} color="#444" />
              <Text style={styles.dateText}>{jobDate.toDateString()}</Text>
            </TouchableOpacity>
            {showDatePicker && (
              <DateTimePicker
                value={jobDate}
                mode="date"
                display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                onChange={handleDateChange}
              />
            )}

            {/* Category Picker */}
            <View style={styles.pickerContainer}>
              <MaterialIcons name="category" size={20} color="#444" style={{ marginRight: 8 }} />
              <Picker
                selectedValue={categoryId}
                onValueChange={(itemValue) => setCategoryId(itemValue)}
                style={styles.picker}
              >
                <Picker.Item label="Select Category" value="" />
                {categories.map((cat) => (
                  <Picker.Item key={cat.id} label={cat.name} value={cat.id} />
                ))}
              </Picker>
            </View>

            <TouchableOpacity onPress={handleUpdate} style={styles.primaryButton}>
              <Text style={styles.buttonText}>Update</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={onClose} style={styles.secondaryButton}>
              <Text style={styles.buttonText}>Cancel</Text>
            </TouchableOpacity>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const FormField = ({ icon, placeholder, value, onChangeText, multiline = false, keyboardType = 'default' }) => (
  <View style={styles.formGroup}>
    <View style={styles.icon}>{icon}</View>
    <TextInput
      placeholder={placeholder}
      value={value}
      onChangeText={onChangeText}
      multiline={multiline}
      keyboardType={keyboardType}
      style={[styles.input, multiline && { height: 80 }]}
      placeholderTextColor="#999"
    />
  </View>
);

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: '#00000066',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalCard: {
    width: '92%',
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    maxHeight: '90%',
    elevation: 12,
  },
  header: {
    fontSize: 20,
    fontWeight: '600',
    color: '#222',
    marginBottom: 18,
    textAlign: 'center',
  },
  formGroup: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F1F3F5',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginBottom: 14,
  },
  icon: {
    marginRight: 10,
  },
  input: {
    flex: 1,
    fontSize: 15,
    color: '#222',
  },
  locationText: {
    fontSize: 13,
    color: '#666',
    marginBottom: 12,
    marginLeft: 6,
  },
  dateField: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F1F3F5',
    borderRadius: 12,
    padding: 12,
    marginBottom: 14,
  },
  dateText: {
    fontSize: 15,
    marginLeft: 10,
    color: '#222',
  },
  pickerContainer: {
    backgroundColor: '#F1F3F5',
    borderRadius: 12,
    marginBottom: 14,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  picker: {
    flex: 1,
    height: 44,
    color: '#222',
  },
  primaryButton: {
    backgroundColor: '#007AFF',
    borderRadius: 50,
    paddingVertical: 14,
    marginTop: 10,
  },
  secondaryButton: {
    backgroundColor: '#B0BEC5',
    borderRadius: 50,
    paddingVertical: 14,
    marginTop: 10,
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 15,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default UpdatePostModal;
