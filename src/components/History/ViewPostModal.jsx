import React from 'react';
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

const ViewPostModal = ({ visible, onClose, post }) => {
  if (!post) return null;

  return (
    <Modal visible={visible} animationType="slide" transparent>
      <View style={styles.overlay}>
        <View style={styles.modal}>
          <Text style={styles.header}>Post Details</Text>
          
          <ScrollView contentContainerStyle={styles.content}>
            <InfoRow icon="title" label="Title" value={post.title} />
            <InfoRow icon="description" label="Description" value={post.description} />
            <InfoRow icon="phone" label="Mobile No" value={post.mobile_no} />
            <InfoRow icon="place" label="Pincode" value={post.pincode} />
            <InfoRow icon="attach-money" label="Amount" value={`₹${post.amount}`} />
            <InfoRow icon="event" label="Job Date" value={post.job_date} />
            <InfoRow icon="location-on" label="Location" value={post.location_name || 'N/A'} />
          </ScrollView>

          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>Close</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const InfoRow = ({ icon, label, value }) => (
  <View style={styles.row}>
    <MaterialIcons name={icon} size={20} color="#3A86FF" style={styles.icon} />
    <Text style={styles.label}>{label}:</Text>
    <Text style={styles.value}>{value || '-'}</Text>
  </View>
);

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: '#00000055',
    justifyContent: 'center',
    padding: 20,
  },
  modal: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    maxHeight: '85%',
    elevation: 6,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  header: {
    fontSize: 20,
    fontWeight: '700',
    color: '#3A86FF',
    marginBottom: 16,
    textAlign: 'center',
  },
  content: {
    paddingBottom: 10,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 14,
    flexWrap: 'wrap',
  },
  icon: {
    marginTop: 2,
    width: 24,
  },
  label: {
    fontWeight: '600',
    color: '#333',
    marginLeft: 8,
    width: 90,
  },
  value: {
    flex: 1,
    color: '#444',
    fontSize: 14,
  },
  closeButton: {
    marginTop: 18,
    backgroundColor: '#3A86FF',
    paddingVertical: 12,
    borderRadius: 8,
  },
  closeButtonText: {
    color: '#FFFFFF',
    textAlign: 'center',
    fontWeight: '600',
    fontSize: 15,
  },
});

export default ViewPostModal;
