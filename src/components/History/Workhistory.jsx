import React, { useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity , Pressable } from 'react-native';
import { MaterialIcons, AntDesign } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';




const Workhistory = ({   icon, color1, color2,workData }) => {
  const navigation = useNavigation();
  const handleView = () => {
    navigation.navigate('ShowPostDetails', { post_id: workData.post_id });
  };
  useEffect(()=>{
    console.log("workData:"+JSON.stringify(workData));
    console.log(workData.status)
  },[]);
  return (
    <View style={styles.container}>
      <View style={styles.infoRow}>
        <MaterialIcons name={icon} size={30} color="#333" style={styles.icon} />
        <View style={styles.details}>
          <Text style={styles.title}>{workData.post.category.name.replace(/^"|"$/g, '')}</Text>
          <View style={styles.statusRow}>
            <Text style={[styles.status, { backgroundColor: color1, color: color2 }]}>
              {workData.status}
            </Text>
            {/* <View style={styles.stars}>
              {Array.from({ length: 5 }, (_, index) => (
                <AntDesign
                  key={index}
                  name={index < ratings ? 'star' : 'staro'}
                  size={14}
                  color="#f0a500"
                />
              ))}
            </View> */}
          </View>
        </View>
      </View>

      <View style={styles.rightColumn}>
        <Text style={styles.date}>{new Date(workData.createdAt).toLocaleString()}</Text>
        {workData.status.toLowerCase() === 'pending' ? (
          <TouchableOpacity style={styles.cancelButton} onPress={handleView}>
            <Text style={styles.viewButtonText}>Cancel</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity style={styles.viewButton} onPress={handleView}>
            <Text style={styles.viewButtonText}>View</Text>
          </TouchableOpacity>
        )}
      </View>

    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 10,
    paddingHorizontal: 16,
    alignItems: 'flex-start',
  },
  infoRow: {
    flexDirection: 'row',
    gap: 10,
    flex: 1,
  },
  icon: {
    paddingTop: 5,
  },
  details: {
    gap: 4,
    flexShrink: 1,
  },
  title: {
    fontSize: 15,
    fontWeight: '600',
    color: '#222',
  },
  statusRow: {
    flexDirection: 'row',
    gap: 6,
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  status: {
    paddingHorizontal: 6,
    borderRadius: 6,
    fontWeight: '600',
    fontSize: 12,
    paddingVertical: 4,
  },
  stars: {
    flexDirection: 'row',
  },
  rightColumn: {
    alignItems: 'flex-end',
    justifyContent: 'space-between',
  },
  date: {
    fontSize: 12,
    fontWeight: '500',
    color: '#697565',
    marginBottom: 6,
  },
  viewButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 4,
    paddingHorizontal: 10,
    borderRadius: 6,
    marginHorizontal:10
  },
  cancelButton: {
    backgroundColor: '#FF3B30',
    paddingVertical: 4,
    paddingHorizontal: 10,
    borderRadius: 6,
    marginHorizontal:10
  },
  viewButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
});

export default Workhistory;
