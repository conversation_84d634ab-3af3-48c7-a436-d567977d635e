import React, { useState,useEffect, useContext } from 'react';
import { View, FlatList,Text, Pressable, Alert } from 'react-native';
import Singlehomework from './SingleHomeWorkDetail';
import HomeworkDetails from './HomeworkDetails';
import { apiRequest } from '../../../utils/api';
import { getUserId } from '../../../utils/storage';
import { useUserForm } from '../../context/UserFormContext';
import { useNavigation } from '@react-navigation/native';
import { AuthContext } from '../../context/AuthProvider';
import { ActivityIndicator } from 'react-native';


export default function HomeworkListScreen() {
  const [selected, setSelected] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [homeWorkData,sethomeWorkData] = useState( []);
  const {formData} = useUserForm();
  const navigation = useNavigation();
  const {logout} = useContext(AuthContext);
  const [current , setcurrent] = useState('1');
  const [totalpages , settotalpages] = useState("0");
  const [loading, setLoading] = useState(true);
  
    const fetchPosts = async (lat, lon) => {
  if (lat && lon) {
    setLoading(true); // start loading
    try {
      const response = await apiRequest(
        'GET',
        `/api/posts/nearby/?page=${current}&lat=${lat}&long=${lon}&radius=100`,
        null
      );
      sethomeWorkData((prev) => {
        const existingIds = new Set(prev.map((post) => post.id));
        const newPosts = response.data.data || [];
        const filteredNewPosts = newPosts.filter((post) => !existingIds.has(post.id));
        return [...prev, ...filteredNewPosts];
      });
      settotalpages(response.data.totalPages);
    } catch (error) {
      console.error('Failed to fetch posts:', error);
    } finally {
      setLoading(false); // done loading
    }
  }
};

  
    useEffect(() => {
      fetchPosts(formData.location_lat , formData.location_long);
    }, []);

  const handleKnowMore = (item) => {
    setSelected(item);
    setShowModal(true);
  };

  const handleCancel = () => {
    setSelected(null);
    setShowModal(false);
  };


  const handleRequest = async() => {
    try {
      const userId = await getUserId();
      console.log("SELECTED : "+JSON.stringify(selected));
       const requestForWork =await apiRequest('POST',`/api/works/request`,{
        "post_id": selected.id
       },logout);
      Alert.alert('Job Requested. Pay ₹10 to confirm after approval.');
      } catch (error) {
        if(error.status == 409){
          Alert.alert("you already requested this work")
        }else{
          Alert.alert("issue in requesting work...try again...")
        }
        
      }
    setShowModal(false);
  };

  const loadmorepost = () => {
  const nextPage = (parseInt(current) + 1).toString();
  setcurrent(nextPage);
  fetchPosts(formData.location_lat , formData.location_long);
}

  return (
    <View style={{ flex: 1, paddingTop: 40 }}>
     <Text style={{fontSize:16,marginLeft:15,fontWeight:500}}>Nearby Posts</Text>
    

    {loading && homeWorkData.length === 0 ? (
  <ActivityIndicator size="large" color="#2563EB" style={{ marginTop: 30 }} />
) : (
  homeWorkData.map((item) => (
    <Singlehomework
      key={item.id}
      id={item.id}
      category={item.title.replace(/^"|"$/g, '')}
      distance={item.distance || 0}
      description={item.description || 'No description provided'}
      amount={item.amount || '00'}
      date={item.job_date}
      onKnowMore={() => handleKnowMore(item)}
    />
  ))
)}

      <Pressable style={{marginBottom:20}} onPress={loadmorepost} >
        <Text style={{textAlign:'center',color:'#2563EB',}} >Load more</Text>
      </Pressable>
      <HomeworkDetails
        visible={showModal}
        data={selected}
        onCancel={handleCancel}
        onRequest={handleRequest}
      />
    </View>
  );
}
