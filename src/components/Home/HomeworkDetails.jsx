// components/ShowWorkDetails.js
import React, { useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  StyleSheet,
  Pressable,
} from 'react-native';
import Feather from '@expo/vector-icons/Feather';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import Entypo from '@expo/vector-icons/Entypo';
import { useUserForm } from '../../context/UserFormContext';

const ShowWorkDetails = ({ visible, data, onCancel, onRequest  , categoryname }) => {

  const {formData} = useUserForm();


  const getDistanceInKm = (userLat, userLon, postLat, postLon) => {
  const toRad = (value) => (value * Math.PI) / 180;

  const R = 6371; // Earth's radius in kilometers
  const dLat = toRad(postLat - userLat);
  const dLon = toRad(postLon - userLon);

  const lat1 = toRad(userLat);
  const lat2 = toRad(postLat);

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1) * Math.cos(lat2) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  const distance = R * c;

  return distance.toFixed(2); // returns distance rounded to 2 decimal places
};

  if (!data) return null;

  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent
      onRequestClose={onCancel}
    >
      
      <View style={styles.overlay}>
        <View style={styles.modal}>
          <Text style={styles.title}>{data.title}</Text>

          <View style={styles.row}>
            <Feather name="file-text" size={20} color="#1F2937" />
            <Text style={styles.detail}>{data.description}</Text>
          </View>

          <View style={styles.row}>
            <Feather name="map-pin" size={20} color="#1F2937" />
            <Text style={styles.detail}>{' '}
              {
                getDistanceInKm(formData.location_lat,formData.location_long , data.location_lat,data.location_long)
              }
           km away</Text>
          </View>

          <View style={styles.row}>
            <Feather name="calendar" size={20} color="#1F2937" />
            <Text style={styles.detail}>
              {new Date(data.createdAt).toLocaleDateString('en-IN', {
            day: 'numeric',
            month: 'short',
            year: 'numeric',
          })}
            </Text>
          </View>

          <View style={styles.row}>
            <MaterialIcons name="currency-rupee"  size={24} color="#1F2937" />
            <Text style={styles.detail}>{data.amount || '--.--'}</Text>
          </View>

          <View style={styles.noticeBox}>
            <Entypo name="info-with-circle" size={18} color="#EF4444" />
            <Text style={styles.notice}>
              Once the user accepts your request, ₹10 must be paid to confirm this job.
            </Text>
          </View>

          <View style={styles.actions}>
            <Pressable style={styles.requestBtn} onPress={onRequest}>
              <Text style={styles.btnText}>Request Job</Text>
            </Pressable>
            <Pressable style={styles.cancelBtn} onPress={onCancel}>
              <Text style={styles.btnText}>Cancel</Text>
            </Pressable>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default ShowWorkDetails;

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.45)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modal: {
    width: '92%',
    maxHeight: '85%',
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 22,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.2,
    shadowRadius: 10,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 18,
    textAlign: 'center',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
    gap: 8,
  },
  detail: {
    fontSize: 16,
    color: '#374151',
    flexShrink: 1,
  },
  noticeBox: {
    marginTop: 16,
    backgroundColor: '#FEE2E2',
    padding: 12,
    borderRadius: 10,
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
  notice: {
    fontSize: 13.5,
    color: '#B91C1C',
    flex: 1,
    lineHeight: 18,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 24,
  },
  requestBtn: {
    backgroundColor: '#2563EB',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 10,
  },
  cancelBtn: {
    backgroundColor: '#9CA3AF',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 10,
  },
  btnText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 15,
  },
});
