import React, { useState } from 'react';
import {
  Modal,
  View,
  TextInput,
  FlatList,
  Text,
  StyleSheet,
  Pressable,
  ActivityIndicator,
} from 'react-native';
import * as ExpoLocation from 'expo-location';
import {GEOAPIFY_API_KEY , GEO_KEY} from '@env'


const LocationPopup = ({ visible, onClose, onSelect }) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);

  const searchPlaces = async (text) => {
    setQuery(text);
    if (text.length < 3) return;

    setLoading(true);
    try {
      const res = await fetch(
        `https://api.geoapify.com/v1/geocode/autocomplete?text=${text}&apiKey=${GEO_KEY}`
      );
      const data = await res.json();
      setResults(data.features);
    //   data.features.map((item) => ({
    //     name: item.properties.formatted,
    //   }))
    } catch (err) {
      console.error('Search error', err);
    }
    setLoading(false);
  };

  const useCurrentLocation = async () => {
    try {
      const { status } = await ExpoLocation.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        alert('Permission denied');
        return;
      }

      const loc = await ExpoLocation.getCurrentPositionAsync({});
      const lat = loc.coords.latitude;
      const lon = loc.coords.longitude;

      const res = await fetch(
        `https://api.geoapify.com/v1/geocode/reverse?lat=${lat}&lon=${lon}&apiKey=${GEO_KEY}`
      );
      const data = await res.json();
      if (data.features.length > 0) {
        const locationName = data.features[0].properties.formatted;
        onSelect(locationName);
      }
    } catch (err) {
      console.error('Current location error', err);
    }
  };

  return (
    <Modal visible={visible} animationType="slide" transparent>
      <View style={styles.overlay}>
        <View style={styles.popup}>
          <TextInput
            keyboardType='numeric'
            placeholder="Search location..."
            style={styles.input}
            value={query}
            onChangeText={searchPlaces}
          />

          <Pressable style={styles.currentBtn} onPress={useCurrentLocation}>
            <Text allowFontScaling={false} style={styles.currentText}>📍 Use My Current Location</Text>
          </Pressable>

          {loading ? (
            <ActivityIndicator style={{ marginTop: 10 }} />
          ) : (
            <FlatList
              data={results}
              keyExtractor={(_, i) => i.toString()}
              renderItem={({ item }) => (
                <Pressable
                  style={styles.resultItem}
                  onPress={() =>{onSelect(item);
                    onClose()
                  } }
                >
                  <Text allowFontScaling={false} style={styles.resultText}>{item.properties.formatted}</Text>
                </Pressable>
              )}
            />
          )}

          <Pressable onPress={onClose} style={styles.closeBtn}>
            <Text allowFontScaling={false} style={styles.closeText}>Close</Text>
          </Pressable>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: '#00000099',
    justifyContent: 'center',
    alignItems: 'center',
  },
  popup: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    elevation: 5,
  },
  input: {
    backgroundColor: '#F3F4F6',
    padding: 12,
    borderRadius: 8,
    fontSize: 16,
    marginBottom: 10,
  },
  currentBtn: {
    backgroundColor: '#E5E7EB',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginBottom: 15,
  },
  currentText: {
    color: '#111827',
    fontSize: 16,
  },
  resultItem: {
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderColor: '#E5E7EB',
  },
  resultText: {
    fontSize: 15,
    color: '#1F2937',
  },
  closeBtn: {
    marginTop: 10,
    alignItems: 'center',
  },
  closeText: {
    color: '#6B7280',
    fontSize: 14,
  },
});

export default LocationPopup;
