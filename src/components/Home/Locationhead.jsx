import React, { useState,useEffect, useContext } from 'react';
import { View, Pressable, Text, StyleSheet } from 'react-native';
import Feather from '@expo/vector-icons/Feather';
import LocationPopup from './LocationPopup';
import { getSearchLocationCache, storelocation, storeSearchLocationCache,getlocation } from '../../../utils/storage';
import { useUserForm } from '../../context/UserFormContext';
import { apiRequest } from '../../../utils/api';
import { AuthContext } from '../../context/AuthProvider';
import AsyncStorage from '@react-native-async-storage/async-storage';

const LocationHead = () => {
  const [visible, setVisible] = useState(false);
  const [locationName, setLocationName] = useState('');
  const {formData , updateForm} = useUserForm();
  const { logout } = useContext(AuthContext);

  const handleSelect = async(item) => {
    const { formatted } = item.properties;
    const [lon, lat] = item.geometry.coordinates;
    

   /*  await storeSearchLocationCache({
      latitude:lat,
      longitude:lon,
      location_name : formatted,
    }) */

      const response = await apiRequest('PUT','/api/users/',{
        location_lat:lat,
        location_long:lon,
      },logout);
      await setLocationName(formatted)
     await storeSearchLocationCache(formatted);
     /* await AsyncStorage.setItem('location',formatted); */
     /* await setlocationname(AsyncStorage.setItem('location',formatted)) */
    setVisible(false);
  };
  useEffect(()=>{
    async function setLocationToStorage() {
    const location = await getSearchLocationCache();
    setLocationName(location);
    }
    setLocationToStorage();
  })
  return (
    <View>
      <Pressable style={styles.button} onPress={() => setVisible(true)}>
        <Feather name="map-pin" size={18} color="#1F2937" />
        <Text allowFontScaling={false} style={styles.text}>
          { locationName || "select Location"}
        </Text> 
      </Pressable>

      <LocationPopup
        visible={visible}
        onClose={() => setVisible(false)}
        onSelect={handleSelect}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  button: {
    marginHorizontal:16,
    padding: 10,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  text: {
    fontSize: 14,
    color: '#1F2937',
  },
});

export default LocationHead;
