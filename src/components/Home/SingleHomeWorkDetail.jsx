import React, { useEffect } from 'react';
import { Pressable, StyleSheet, Text, View } from 'react-native';
import Feather from '@expo/vector-icons/Feather';
import Entypo from '@expo/vector-icons/Entypo';

const Singlehomework = ({ id, category, distance, description, amount, date, onKnowMore }) => {
  
  return (
    <View style={styles.card}>
      <View style={styles.header}>
        <Text style={styles.category}>{category}</Text>
        <Text style={styles.distance}>
          {
            distance ?<Feather name="map-pin" size={14} color="#4B5563" /> : ""
          }
          {
            distance ? `${Number(distance).toFixed(1)} km away` : ""
          }
        </Text>
      </View>

      <View style={styles.meta}>
        <Text style={styles.amount}>₹ {amount || '--.--'}</Text>
        <Text style={styles.date}>
          {new Date(date).toLocaleDateString('en-IN', {
            day: 'numeric',
            month: 'short',
            year: 'numeric',
          })}
        </Text>
      </View>

      <Text style={styles.description} numberOfLines={2}>
        {description}
      </Text>

      <Pressable style={styles.cta} onPress={onKnowMore}>
        <Text style={styles.ctaText}>
          Know more <Entypo name="chevron-right" size={16} color="#2563EB" />
        </Text>
      </Pressable>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    marginHorizontal: 20,
    marginVertical: 12,
    padding: 16,
    // backgroundColor: '#F9FAFB',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    // elevation: 1,
    borderColor: '#E5E7EB',
    borderWidth: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  category: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  distance: {
    fontSize: 13,
    color: '#4B5563',
  },
  meta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 6,
  },
  amount: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
  },
  date: {
    fontSize: 13,
    color: '#6B7280',
  },
  description: {
    marginTop: 12,
    fontSize: 14,
    color: '#374151',
  },
  cta: {
    marginTop: 10,
    alignSelf: 'flex-start',
  },
  ctaText: {
    color: '#2563EB',
    fontWeight: '500',
    fontSize: 14,
  },
});

export default Singlehomework;
