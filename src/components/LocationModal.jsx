import { Modal, View, Button, Text } from 'react-native';

export default function LocationModal({ visible, onClose, onGetLocation }) {
  return (
    <Modal visible={visible} animationType="fade">
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
        <Text style={{ fontSize: 18, marginBottom: 20 }}>Allow access to current location?</Text>
        <Button title="Get Location" onPress={onGetLocation} />
        <Button title="Cancel" color="red" onPress={onClose} />
      </View>
    </Modal>
  );
}
