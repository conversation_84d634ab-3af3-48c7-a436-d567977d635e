import React from 'react';
import { View, Pressable, Text, StyleSheet } from 'react-native';

const ActionButtons = ({ paid, onConfirm, onCancel }) => {
  return (
    <View style={styles.container}>
      {paid ? (
        <View style={styles.paidContainer}>
          <Text style={styles.paidEmoji}>✅</Text>
          <Text style={styles.paidText}>You have already paid</Text>
        </View>
      ) : (
        <Pressable
          style={({ pressed }) => [
            styles.confirmButton,
            pressed && styles.pressedButton,
          ]}
          onPress={onConfirm}
        >
          <Text style={styles.confirmText}>Confirm & Pay ₹10</Text>
        </Pressable>
      )}
      {
        paid ? (<></>) : (<Pressable
        style={({ pressed }) => [
          styles.cancelButton,
          pressed && styles.pressedButton,
        ]}
        onPress={onCancel}
      >
        <Text style={styles.cancelText}>Cancel</Text>
      </Pressable>)
      }

      
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 24,
    gap: 12,
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  confirmButton: {
    backgroundColor: '#2563eb',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 8,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  cancelButton: {
    backgroundColor: '#e11d48',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 8,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.15,
    shadowRadius: 3,
    elevation: 2,
  },
  confirmText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  cancelText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  paidContainer: {
    backgroundColor: '#e6fffa',
    borderColor: '#38b2ac',
    borderWidth: 1,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    width: '100%',
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  paidEmoji: {
    fontSize: 20,
  },
  paidText: {
    fontSize: 16,
    color: '#2c7a7b',
    fontWeight: '600',
  },
  pressedButton: {
    opacity: 0.85,
    transform: [{ scale: 0.98 }],
  },
});

export default ActionButtons;
