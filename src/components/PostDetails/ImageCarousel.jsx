import React, { useRef, useState } from 'react';
import {
  View,
  ScrollView,
  Image,
  StyleSheet,
  Dimensions,
  Pressable,
  Text,
} from 'react-native';
import { Feather } from '@expo/vector-icons';

const { width } = Dimensions.get('window');

const ImageCarousel = ({ images = [] }) => {
  const scrollRef = useRef(null);
  const [currentIndex, setCurrentIndex] = useState(0);

  const scrollToIndex = (index) => {
    const x = index * (width - 32);
    scrollRef.current?.scrollTo({ x, animated: true });
    setCurrentIndex(index);
  };

  const goNext = () => {
    if (currentIndex < images.length - 1) {
      scrollToIndex(currentIndex + 1);
    }
  };

  const goPrev = () => {
    if (currentIndex > 0) {
      scrollToIndex(currentIndex - 1);
    }
  };

  return (
    <View style={styles.wrapper}>
      {/* <ScrollView
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        ref={scrollRef}
        onScroll={(e) => {
          const index = Math.round(
            e.nativeEvent.contentOffset.x / (width - 32)
          );
          setCurrentIndex(index);
        }}
        scrollEventThrottle={16}
        style={styles.scroll}
        contentContainerStyle={styles.scrollContent}
      >
        {[{
    "image_url":"https://storage.googleapis.com/flask-test-3d74a.appspot.com/uploads/file_54551c30-609a-4f65-a4fc-2bb39a85eb29_madxh3fj-x51ln",
    


  },{
    "image_url":"https://storage.googleapis.com/flask-test-3d74a.appspot.com/uploads/file_54551c30-609a-4f65-a4fc-2bb39a85eb29_madxh3fj-x51ln",
    


  }].map((img, index) => (
          <Image
            key={index}
            source={{ uri: img.image_url }}
            style={styles.image}
            resizeMode="full"
          />
        ))}
      </ScrollView>

      {images.length > 1 && (
        <>
          <Pressable style={styles.leftArrow} onPress={goPrev}>
            <Feather name="chevron-left" size={28} color="#fff" />
          </Pressable>
          <Pressable style={styles.rightArrow} onPress={goNext}>
            <Feather name="chevron-right" size={28} color="#fff" />
          </Pressable>
        </>
      )} */}

      <Image src={require('../../../assets/postimage.jpg')} />
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    position: 'relative',
    marginBottom: 16,
    marginTop: '10%'
  },
  scroll: {
    borderRadius: 10,
  },
  scrollContent: {
    paddingHorizontal: 16,
  },
  image: {
    width: width - 32,
    height: 200,
    borderRadius: 10,
    marginRight: 12,
  },
  leftArrow: {
    position: 'absolute',
    top: '45%',
    left: 8,
    backgroundColor: 'rgba(0,0,0,0.5)',
    padding: 6,
    borderRadius: 20,
    zIndex: 10,
  },
  rightArrow: {
    position: 'absolute',
    top: '45%',
    right: 8,
    backgroundColor: 'rgba(0,0,0,0.5)',
    padding: 6,
    borderRadius: 20,
    zIndex: 10,
  },
});

export default ImageCarousel;
