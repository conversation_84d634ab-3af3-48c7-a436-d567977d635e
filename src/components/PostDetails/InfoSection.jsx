import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Entypo, Feather } from '@expo/vector-icons';

const InfoSection = ({ post, showContact }) => {
  if (!post) {
    return (
      <View style={styles.section}>
        <Text style={styles.errorText}>⚠️ Error loading post information.</Text>
      </View>
    );
  }

  const {
    title = 'No Title',
    description = 'No description provided.',
    location_name = 'Not Provided',
    job_date,
    amount = 'N/A',
    working_hour = 'Not Provided',
    no_of_workers = 'N/A',
    mobile_no = 'Hidden',
  } = post;

  return (
    <View style={styles.section}>
      <Text style={styles.title}>{title}</Text>
      <Text style={styles.description}>{description}</Text>

      <View style={styles.row}>
        <Entypo name="location-pin" size={20} color="#555" style={styles.icon} />
        <Text style={styles.valueText}>{location_name}</Text>
      </View>

      <View style={styles.row}>
        <Feather name="calendar" size={18} color="#555" style={styles.icon} />
        <Text style={styles.valueText}>
          {job_date ? new Date(job_date).toLocaleDateString() : 'Date not available'}
        </Text>
      </View>

      <View style={styles.row}>
  <Feather name="dollar-sign" size={18} color="#555" style={styles.icon} />
  <Text style={styles.label}>Amount: </Text>
  <Text style={styles.value}>₹{amount}</Text>
</View>

<View style={styles.row}>
  <Feather name="clock" size={18} color="#555" style={styles.icon} />
  <Text style={styles.label}>Working Hours: </Text>
  <Text style={styles.value}>{working_hour}</Text>
</View>

<View style={styles.row}>
  <Feather name="users" size={18} color="#555" style={styles.icon} />
  <Text style={styles.label}>Workers Required: </Text>
  <Text style={styles.value}>{no_of_workers}</Text>
</View>

{showContact ? (
  <View style={styles.row}>
    <Feather name="phone" size={18} color="#0077b6" style={styles.icon} />
    <Text style={styles.contactLabel}>Contact:</Text>
    <Text style={styles.contactText}>{mobile_no}</Text>
  </View>
) : (
  <View style={styles.row}>
    <Feather name="lock" size={18} color="#999" style={styles.icon} />
    <Text style={styles.payNote}>Pay ₹10 to view contact information.</Text>
  </View>
)}

    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: 24,
    paddingHorizontal: 8,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#222',
    marginBottom: 6,
  },
  description: {
    fontSize: 16,
    color: '#444',
    marginBottom: 12,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  icon: {
    marginRight: 6,
  },
  valueText: {
    fontSize: 15,
    color: '#555',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 4,
  },
  label: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333',
  },
  value: {
    fontSize: 15,
    color: '#333',
  },
  contactRow: {
    marginTop: 14,
    padding: 10,
    backgroundColor: '#e6f7ff',
    borderRadius: 6,
  },
  contactLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#0077b6',
  },
  contactText: {
    fontSize: 16,
    color: '#0077b6',
    marginTop: 4,
  },
  payNote: {
    fontStyle: 'italic',
    fontSize: 14,
    color: '#999',
    marginTop: 12,
  },
  errorText: {
    color: 'red',
    fontSize: 16,
    textAlign: 'center',
    paddingVertical: 10,
  },
});

export default InfoSection;
