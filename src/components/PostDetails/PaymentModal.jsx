import React from 'react';
import { View, Pressable, Text, StyleSheet } from 'react-native';
import { WebView } from 'react-native-webview';

const PaymentModal = ({ visible, orderId, user, onClose, onMessage }) => {
  if (!orderId) return null;

  const paymentHTML = `
    <html>
    <head><meta name="viewport" content="width=device-width, initial-scale=1.0"/></head>
    <body>
      <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
      <script>
        var options = {
          key: "rzp_live_KdgCnlP1DrVQAC",
          amount: "${10 * 100}",
          currency: "INR",
          name: "Payment for APP Commission",
          description: "Pay ₹10 to unlock contact Details of Post",
          order_id: "${orderId}",
          handler: function (response) {
            window.ReactNativeWebView.postMessage(JSON.stringify(response));
          },
          prefill: {
            name: "${user?.name || ''}",
            email: "${user?.email || ''}",
            contact: "${user?.mobile || ''}"
          },
          theme: { color: "#0a74da" }
        };
        var rzp = new Razorpay(options);
        rzp.open();
      </script>
    </body>
    </html>
  `;

  return (
    <View style={{ flex: 1 }}>
      <Pressable style={styles.closeButton} onPress={onClose}>
        <Text style={styles.closeText}>✖ Close</Text>
      </Pressable>
      <WebView
        originWhitelist={['*']}
        source={{ html: paymentHTML }}
        onMessage={onMessage}
        javaScriptEnabled
        domStorageEnabled
      />
    </View>
  );
};

const styles = StyleSheet.create({
  closeButton: {
    padding: 12,
    backgroundColor: '#f5f5f5',
    alignItems: 'flex-end',
  },
  closeText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default PaymentModal;
