import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';

const LogoutButton = ({ onLogout }) => (
  <View style={styles.container}>
    <TouchableOpacity style={styles.link} onPress={onLogout}>
      <Icon name="log-out" size={18} color="#e53935" style={styles.icon} />
      <Text style={styles.linkText}>Logout</Text>
    </TouchableOpacity>
  </View>
);

const styles = StyleSheet.create({
  container: {
    padding: 10,
    alignItems: 'flex-start',
  },
  link: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 10,
  },
  icon: {
    marginRight: 10,
  },
  linkText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#e53935',
  },
});

export default LogoutButton;
