import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, Pressable } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import { useNavigation } from "@react-navigation/native";
const QuickLinks = () => {
    const navigation = useNavigation();
  const [links ,setlinks ] = useState([
    { key: 'Home', label: 'Home', icon: 'home' },
    { key: 'Notifications', label: 'Notifications', icon: 'bell' },
    { key: 'Terms', label: 'Terms and Conditions', icon: 'file-text' },
    { key: 'Privacy', label: 'Privacy Policy', icon: 'shield' }
  ]
  
  );

  const handlePress = (key) => {
    navigation.navigate(key)
   
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Quick Links</Text>
     {/*  <FlatList
        data={links}
        keyExtractor={(item) => item.key}
        renderItem={({ item }) => (
          <TouchableOpacity style={styles.linkItem} onPress={() => handlePress(item.key)}>
            <Icon name={item.icon} size={20} color="#000" style={styles.icon} />
            <Text style={styles.label}>{item.label}</Text>
          </TouchableOpacity>
        )}
      /> */}
      {
        links.map((item)=>(
          <Pressable key={item.icon} style={styles.linkItem} onPress={() => handlePress(item.key)}>
            <Icon name={item.icon} size={20} color="#000" style={styles.icon} />
            <Text style={styles.label} allowFontScaling={false}>{item.label}</Text>
          </Pressable>
        ))
      }
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 10,
    padding: 10,
    backgroundColor: '#fff',
    borderRadius: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  linkItem: {
    width: '100%',
    display:'flex',
   flexDirection: 'row',
  alignItems: 'center',
  paddingVertical: 10,
  borderBottomColor: '#eee',
  borderBottomWidth: 1,
    
  },
  icon: {
    marginRight: 12,
  },
  label: {
    fontSize: 16,
    color: '#333',
    flexShrink: 1,
    flex: 1,
    flexWrap: 'nowrap', 
  },
});

export default QuickLinks;
