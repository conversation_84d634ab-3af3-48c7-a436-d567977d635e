// components/SettingsToggles.js
import React from 'react';
import { View, Text, StyleSheet, Switch } from 'react-native';

const SettingsToggles = ({
  isLocationEnabled,
  setIsLocationEnabled,
  isPushEnabled,
  setIsPushEnabled,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.toggleRow}>
        <Text style={styles.label}>Location Access</Text>
        <Switch
          value={isLocationEnabled}
          onValueChange={setIsLocationEnabled}
        />
      </View>
      <View style={styles.toggleRow}>
        <Text style={styles.label}>Push Notifications</Text>
        <Switch value={isPushEnabled} onValueChange={setIsPushEnabled} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    marginTop: 20,
  },
  toggleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 14,
    borderBottomWidth: 1,
    borderColor: '#e0e0e0',
  },
  label: {
    fontSize: 16,
    color: '#444',
  },
});

export default SettingsToggles;
