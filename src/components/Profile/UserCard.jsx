import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';

const UserCard = ({ user,locationName,  onEditPress, onLocationPress }) => {
  const firstLetter = user.name?.charAt(0)?.toUpperCase() || 'A';

  return (
    <View style={styles.card}>
      <View style={styles.row}>
        <View style={styles.avatarContainer}>
          <View style={styles.avatar}>
            <Text style={styles.avatarText}>{firstLetter}</Text>
          </View>
          <Text style={styles.avatarLabel}>Profile</Text>
        </View>

        <View style={styles.infoSection}>
          <Text style={styles.name}>{user.name || 'No Name'}</Text>

          <View style={styles.rowItem}>
            <Icon name="mail" size={16} color="#000" />
            <Text style={styles.text}>{user.email_id || 'No Email'}</Text>
          </View>

          <View style={styles.rowItem}>
            <Icon name="phone" size={16} color="#000" />
            <Text style={styles.text}>  +91 {user.phone_no || 'No Phone'}</Text>
          </View>

          <TouchableOpacity >
            <View style={styles.rowItem}>
              <Icon name="map-pin" size={16} color="#000" />
              <Text style={styles.location}>
                {locationName|| 'Select Location'}
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.divider} />

      <TouchableOpacity onPress={onEditPress}>
        <Text style={styles.edit}>Edit Profile</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    margin: 16,
    padding: 20,
    borderRadius: 16,
    backgroundColor: '#fff',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  avatarContainer: {
    alignItems: 'center',
    marginRight: 16,
  },
  avatar: {
    backgroundColor: '#000',
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
  },
  avatarLabel: {
    marginTop: 6,
    fontSize: 12,
    color: '#666',
  },
  infoSection: {
    flex: 1,
  },
  name: {
    fontSize: 20,
    fontWeight: '700',
    color: '#222',
    marginBottom: 8,
  },
  rowItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  text: {
    fontSize: 15,
    color: '#555',
    marginLeft: 8,
  },
  location: {
    fontSize: 15,
    color: '#000',
    marginLeft: 8,
  },
  divider: {
    height: 1,
    backgroundColor: '#eee',
    marginVertical: 12,
  },
  edit: {
    textAlign: 'right',
    color: '#007bff',
    fontWeight: '600',
    fontSize: 15,
  },
});

export default UserCard;
