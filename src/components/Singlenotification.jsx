import React from 'react'
import { Image, Text, View  } from 'react-native'
import { 
  AntDesign, Entypo, EvilIcons, Feather, FontAwesome, FontAwesome5, FontAwesome6, 
  Fontisto, Foundation, Ionicons, MaterialCommunityIcons, MaterialIcons, 
  Octicons, SimpleLineIcons, Zocial 
} from '@expo/vector-icons';



const Singlenotification = ({title , description , timestamp,icon}) => {
  return (
   <View style={{display:'flex',flexDirection:'row',alignItems:'flex-start',justifyContent:'space-between',marginVertical:10}} >
    <View style={{display:'flex',flexDirection:'row',gap:10}} >
        <View style={{paddingTop:5}} >
        <FontAwesome6 name={icon} size={25} color="black" />
        </View>
        <View style={{gap:5}} >
            <Text style={{fontSize:15,fontWeight:500}} >{title}</Text>
            <Text style={{width:210,fontSize:13,fontWeight:400}} >{description}</Text>
        </View>
    </View>
    <View>
        <Text style={{fontSize:12,fontWeight:500,color:'#697565'}} >{timestamp}</Text>
    </View>
   </View>
  )
}

export default Singlenotification
