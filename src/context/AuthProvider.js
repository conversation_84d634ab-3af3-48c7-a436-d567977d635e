// src/context/AuthContext.js

import React, { createContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { removeLocationcache, storeCategoriesCache, storeNotificationCache, storeSearchLocationCache } from '../../utils/storage';

export const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [isLoggedIn, setIsLoggedIn] = useState(null);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [password, setpassworduh] = useState('');

  useEffect(() => {
    const checkLogin = async () => {
      const value = await AsyncStorage.getItem('isLoggedIn');
      setIsLoggedIn(value === 'true');
    };
    checkLogin();
  }, []);

  const login = async () => {
    await AsyncStorage.setItem('isLoggedIn', 'true');
    setIsLoggedIn(true);
  };

  const logout = async () => {
    await AsyncStorage.multiRemove([
      'accessToken',
      'refreshToken',
      'userId',
      'isLoggedIn',
    ]);
    setPhoneNumber('');
    setpassworduh('');
    setIsLoggedIn(false);
    await storeCategoriesCache(null);
    await storeNotificationCache(null);
    await storeSearchLocationCache(null);
  };

  return (
    <AuthContext.Provider
      value={{
        isLoggedIn,
        login,
        logout,
        phoneNumber,
        password,
        setPhoneNumber,
        setpassworduh,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
