import React, { createContext, useState, useContext } from 'react';

const UserFormContext = createContext();

export const UserFormProvider = ({ children }) => {
  const [formData, setFormData] = useState({
    phone_no: '',
    email_id: '',
    pushnotification_id: '',
    role: '',
    name: '',
    location_lat: null,
    location_long: null,
    is_active: true,
    is_verified: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    location_name : "",
    known_works:[],
  });

  const updateForm = (key, value) => {
  setFormData(prev => ({ ...prev, [key]: value }));
};

const setFullFormData = (data) => {
  setFormData(data);
};

  console.log('📦 UserFormProvider mounted');


  return (
    <UserFormContext.Provider value={{ formData, updateForm ,setFullFormData}}>
      {children}
    </UserFormContext.Provider>
  );
};

export const useUserForm = () => useContext(UserFormContext);
