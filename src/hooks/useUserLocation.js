// useUserLocation.js
import { useEffect, useState } from 'react';
import { Alert } from 'react-native';
import * as Location from 'expo-location';

const useUserLocation = () => {
  const [location, setLocation] = useState(null);
  const [errorMsg, setErrorMsg] = useState(null);

  useEffect(() => {
    (async () => {
      try {
        // Check if location services are enabled
        const isLocationEnabled = await Location.hasServicesEnabledAsync();
        if (!isLocationEnabled) {
          setErrorMsg('Location services are disabled');
          Alert.alert('Location Services', 'Please enable location services in your device settings.');
          return;
        }

        const { status } = await Location.requestForegroundPermissionsAsync();

        if (status !== 'granted') {
          setErrorMsg('Permission to access location was denied');
          Alert.alert('Permission Denied', 'We need your location to show nearby services.');
          return;
        }

        // Use lower accuracy for better compatibility with older devices
        const loc = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Balanced,
          timeout: 15000,
          maximumAge: 10000,
        });
        setLocation(loc.coords);
      } catch (error) {
        setErrorMsg(error.message || 'Something went wrong while fetching location');
        console.log('Location Error:', error);

        // Fallback: try with lower accuracy
        try {
          const loc = await Location.getCurrentPositionAsync({
            accuracy: Location.Accuracy.Low,
            timeout: 10000,
          });
          setLocation(loc.coords);
        } catch (fallbackError) {
          console.log('Fallback Location Error:', fallbackError);
        }
      }
    })();
  }, []);

  return { location, errorMsg };
};

export default useUserLocation;
