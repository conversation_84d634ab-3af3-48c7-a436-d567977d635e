// useUserLocation.js
import { useEffect, useState } from 'react';
import { Alert } from 'react-native';
import * as Location from 'expo-location';

const useUserLocation = () => {
  const [location, setLocation] = useState(null);
  const [errorMsg, setErrorMsg] = useState(null);

  useEffect(() => {
    (async () => {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();

        if (status !== 'granted') {
          setErrorMsg('Permission to access location was denied');
          Alert.alert('Permission Denied', 'We need your location to show nearby services.');
          return;
        }

        const loc = await Location.getCurrentPositionAsync({});
        setLocation(loc.coords);
      } catch (error) {
        setErrorMsg(error.message || 'Something went wrong while fetching location');
        console.log('Location Error:', error);
      }
    })();
  }, []);

  return { location, errorMsg };
};

export default useUserLocation;
