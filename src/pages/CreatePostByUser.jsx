import React, { useContext, useEffect, useState } from 'react';
import { 
  Platform, Pressable, SafeAreaView, ScrollView, 
  StatusBar, StyleSheet, Text, TextInput, View, 
  Alert, Image, ActivityIndicator 
} from 'react-native';
import Header from '../components/Header';
import { Picker } from '@react-native-picker/picker';
import * as ImagePicker from 'expo-image-picker';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useNavigation } from '@react-navigation/native';
import { debounce } from 'lodash';
import { GEOAPIFY_API_KEY } from '@env';
import { TouchableOpacity } from 'react-native';
import { apiRequest } from '../../utils/api';
import { AuthContext } from '../context/AuthProvider';
import { useUserForm } from '../context/UserFormContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { multipart } from '../../utils/multipartapi';

const Post = () => {
  const { logout } = useContext(AuthContext);
  const { setFullFormData, formData } = useUserForm();
  const [userID, setUserID] = useState(null);
  const [categoryData, setCategoryData] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState({ id: '', name: '' });
  const navigation = useNavigation();
  const [images, setImages] = useState([]);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);
  
  // Form state variables
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [pincode, setPincode] = useState('');
  const [amount, setAmount] = useState('');
  const [phone, setPhone] = useState('');
  const [workingHours, setWorkingHours] = useState('');
  const [noOfWorkers, setNoOfWorkers] = useState('');
  const [locationLat, setLocationLat] = useState(null);
  const [locationLong, setLocationLong] = useState(null);
  const [locationName, setLocationName] = useState(null);
  const [date, setDate] = useState(new Date());
  const [address , setAddress] = useState('')
  const [isshow , setisshow] = useState(false);

  // Fetch user ID from AsyncStorage
  useEffect(() => {
    const fetchUserID = async () => {
      const id = await AsyncStorage.getItem('userId');
      setUserID(id);
    };
    fetchUserID();
  }, []);
  
  useEffect(()=>{
    const fetchisshow = async ()=>{
      try{
      const res = await apiRequest('GET','/api/posts/is-show',null,logout);
      await setisshow(res.data.data.is_show);
    }catch(error){
      console.log("error",error)
    }
    }
    fetchisshow();
  },[])

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await apiRequest('GET', '/api/categories', null, logout);
        const cleanedData = response.data.data.map(item => ({
          id: item.id,
          name: item.name.replace(/['"]+/g, ''), // Clean up name
        }));
        setCategoryData(cleanedData);
      } catch (error) {
        console.error("Error fetching category data", error);
      }
    };
    fetchCategories();
  }, []);

  // Fetch location suggestions
  useEffect(() => {
    const fetchSuggestions = debounce(async () => {
      if (query.length < 3) return;
      try {
        const response = await fetch(
          `https://api.geoapify.com/v1/geocode/autocomplete?text=${encodeURIComponent(query)}&limit=5&apiKey=${GEOAPIFY_API_KEY}`
        );
        const data = await response.json();
        setSuggestions(data.features || []);
      } catch (error) {
        console.error('Error fetching location suggestions:', error);
      }
    }, 300);
    
    fetchSuggestions();
    return () => fetchSuggestions.cancel(); // Clean up
  }, [query]);

  // Handle image selection
  const pickImage = async () => {
    if (images.length >= 1) {
      Alert.alert("Limit Reached", "You can only upload up to 1 images.");
      return;
    }
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      quality: 0.7,
    });
    if (!result.canceled) {
      setImages([...images, result.assets[0]]);
    }
  };

  // Fetch location name based on pincode
  const fetchLocationName = async (pincode) => {
    try {
      const response = await fetch(`https://api.geoapify.com/v1/geocode/search?postcode=${pincode}&apiKey=${GEOAPIFY_API_KEY}`);
      const data = await response.json();
      if (data.features.length > 0) {
        setLocationName(data.features[0].properties.formatted);
      } else {
        setLocationName(null);
      }
    } catch (error) {
      console.error('Error fetching location name:', error);
    }
  };


  //post images with id
  const postImage = async (id) => {
  try {
    // Prepare form data
    const formData = new FormData();

    await formData.append("files", {
      uri: images[0].uri,
      name:'image.jpg',
      type: 'image/jpeg',
    });

    // Get accessToken from AsyncStorage
   const response = await multipart('POST',`/api/posts/${id}/images`,formData,logout);

/*     console.log("✅ Image uploaded:", response.data); 
 */     return response.data;

  } catch (error) {
      console.error("🔴 Error uploading image:", {
       message: error.message,
       status: error.response?.status,
       data: error.response?.data,
}); 
   throw error;
  }
};

  // Handle post submission
  const postWork = async () => {
    // Validate inputs
    if (!selectedCategory.id) {
      Alert.alert("Category Error", "Please select a valid category.");
      return;
    }
    if ( !description || !pincode || !amount || !phone || !workingHours || !noOfWorkers || !locationLat || !locationLong) {
      Alert.alert("Missing Fields", "Please fill in all required fields.");
      return;
    }
    if (!/^\d{6}$/.test(pincode)) {
      Alert.alert("Pincode Error", "Please enter a valid 6-digit pincode for Tamil Nadu.");
      return;
    }

    // Fetch location name based on pincode
    await fetchLocationName(pincode);

    setLoading(true);
    try {
      const response = await apiRequest('POST', '/api/posts', {
        user_id: userID,
        category_id: selectedCategory.id,
        title : selectedCategory.name,
        description,
        pincode,
        amount,
        mobile_no: phone,
        working_hour: workingHours,
        no_of_workers: noOfWorkers,
        is_show: true,
        status: 'pending',
        location_lat: locationLat,
        location_long: locationLong,
        location_name: locationName,
        job_date: date.toISOString(), // Convert date to ISO string
      }, logout);
/*        await postImage(response.data.data.id);
 */       console.log(response.data.data.id)
Alert.alert('POST Success:', response.data.message);
      // Optionally navigate or reset form
    } catch (error) {
      Alert.alert("post failed try again later!")
      console.error('POST Error:', error.response?.data || error.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle category selection
  const handleCategoryChange = (selectedId) => {
    const selected = categoryData.find(cat => cat.id === selectedId);
    if (selected) {
      setSelectedCategory({ id: selected.id, name: selected.name });
    }
  };

  // Handle date picker
  const onChangeDate = (event, selectedDate) => {
    const currentDate = selectedDate || date;
    setShowDatePicker(Platform.OS === 'ios');
    setDate(currentDate);
  };

  const showDatepicker = () => {
    setShowDatePicker(true);
  };

  const removeimage = ()=>{
     Alert.alert(
    'Remove Images',
    'Do you want to remove image?',
    [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Yes',
        onPress: () => setImages([]),
        style: 'destructive',
      },
    ],
    { cancelable: true }
  );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <StatusBar barStyle="dark-content" />
        <Header />
        {
          isshow ? (
            <View style={styles.formContainer}>
          <Text style={styles.title}>Post a new work</Text>
         {/* <Text style={styles.label}>Upload image</Text>
           <Pressable onPress={pickImage}>
            <View style={styles.imagePicker}>
              <Text><Feather name="image" size={20} color="#e0e8f2" /></Text>
              <Text>Pick Image</Text>
            </View>
          </Pressable> */}
          <Pressable style={styles.imagePreview} onPress={removeimage} >
            {images.map((img, index) => (
              <Image key={index} source={{ uri: img.uri }} style={styles.image} />
            ))}
          </Pressable>
          <Text style={styles.label}>Type</Text>
          <View style={styles.input}>
            <Picker selectedValue={selectedCategory.id} onValueChange={handleCategoryChange}>
              {categoryData.map(category => (
                <Picker.Item key={category.id} label={category.name} value={category.id} />
              ))}
            </Picker>
          </View>
          {/* <Text style={styles.label}>Title</Text>
          <TextInput style={styles.inputField} onChangeText={setTitle} placeholder='Title' /> */}
          <Text style={styles.label}>About your work</Text>
          <TextInput style={styles.inputField} onChangeText={setDescription} placeholder='About' />
          <Text style={styles.label}>Location-Pincode</Text>
          <TextInput style={styles.inputField} value={query} onChangeText={setQuery} placeholder='Pincode' keyboardType='numeric' />
          {suggestions.map(item => (
            <View key={item.properties.place_id} style={styles.suggestion}>
              <TouchableOpacity onPress={() => {
                setPincode(item.properties.postcode);
                setQuery(item.properties.formatted);
                setLocationLat(item.properties.lat);
                setLocationLong(item.properties.lon);
                setSuggestions([]);
              }}>
                <Text>{item.properties.formatted}</Text>
              </TouchableOpacity>
            </View>
          ))}
          <Text style={styles.label} >Address</Text>
          <View style={{ height:70,
               borderColor:'#e0e8f2',
               borderWidth:1,
               borderRadius:8,
               justifyContent:'center',
               paddingHorizontal:10}} >
                <TextInput
                  style={{width:'100%',fontSize:13,paddingVertical:8}}
                  placeholder="Enter your full address"
                  value={address}
                  onChangeText={setAddress}
                  multiline
                  numberOfLines={4} // optional, for initial height
                  textAlignVertical="top" // makes sure text starts from top
                />
                </View>
          <Text style={styles.label}>Amount</Text>
          <TextInput style={styles.inputField} onChangeText={setAmount} placeholder='Amount' keyboardType='numeric' />
          <Text style={styles.label}>Mobile No</Text>
          <TextInput style={styles.inputField} onChangeText={setPhone} maxLength={10} placeholder='Mobile no' keyboardType='numeric' />
          <Text style={styles.label}>Working Timing</Text>
          <TextInput style={styles.inputField} onChangeText={setWorkingHours} placeholder='Working Hours (e.g., 9 AM - 5 PM)' />
          <Text style={styles.label}>Job Date</Text>
          <Pressable style={styles.input} onPress={showDatepicker}>
            <Text style={styles.dateText}>{date.toDateString()}</Text>
          </Pressable>
          {showDatePicker && (
            <DateTimePicker
              value={date}
              mode="date"
              display="calendar"
              onChange={onChangeDate}
            />
          )}
          <Text style={styles.label}>No of workers</Text>
          <TextInput style={styles.inputField} onChangeText={setNoOfWorkers} placeholder='No of workers' keyboardType='numeric' />
          <View style={styles.buttonContainer}>
            <Pressable style={styles.submitButton} onPress={()=>postWork()} disabled={loading}>
              {loading ? <ActivityIndicator color="#fff" /> : <Text style={styles.buttonText}>Post Work</Text>}
            </Pressable>
            <Pressable onPress={() => navigation.navigate('Home')} style={styles.cancelButton}>
              <Text style={styles.buttonTextone}>Cancel</Text>
            </Pressable>
          </View>
        </View>

          ) : (
            <View style={{paddingHorizontal:16,paddingTop:40}} ><Text>This feature is currently turned off...you will able to post in the upcoming days</Text></View>
          )
        }
        
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
    backgroundColor: '#fff',
  },
  formContainer: {
    marginHorizontal: 16,
  },
  title: {
    fontSize: 14,
    fontWeight: '500',
    paddingVertical: 10,
  },
  label: {
    fontSize: 13,
    paddingTop: 10,
    paddingBottom: 5,
    fontWeight: '500',
  },
  imagePicker: {
    flexDirection: 'row',
    gap: 12,
    borderColor: '#e0e8f2',
    borderWidth: 1,
    borderRadius: 12,
    padding: 10,
  },
  imagePreview: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  image: {
    width: 100,
    height: 100,
    marginTop: 10,
  },
  input: {
    height: 42,
    borderColor: '#e0e8f2',
    borderWidth: 1,
    borderRadius: 8,
    justifyContent: 'center',
    paddingHorizontal: 10,
  },
  inputField: {
    width: '100%',
    fontSize: 13,
    borderColor: '#e0e8f2',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    height: 42,
  },
  suggestion: {
    paddingVertical: 8,
  },
  dateText: {
    fontWeight: '500',
  },
  buttonContainer: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 20,
  },
  submitButton: {
    width: '48%',
    alignItems: 'center',
    backgroundColor: '#000',
    paddingVertical: 12,
    borderRadius: 8,
  },
  cancelButton: {
    width: '48%',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingVertical: 12,
    borderColor: '#e0e8f2',
    borderWidth: 2,
    borderRadius: 10,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  buttonTextone: {
    color: '#000',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default Post;
