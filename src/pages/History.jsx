import React, { useContext, useEffect, useState } from 'react'
import { Alert, Platform, Pressable, SafeAreaView, ScrollView, StatusBar, StyleSheet, Text, View } from 'react-native'
import Header from '../components/Header'
import Workhistory from '../components/History/Workhistory'
import Posthistory from '../components/History/Posthistory'
import { apiRequest } from '../../utils/api'
import { AuthContext } from '../context/AuthProvider'
import {getUserId} from '../../utils/storage';

const History = () => {
  const [activeTab, setActiveTab] = useState('Work History');
  const [workHistroyData,setWorkHistroyData] = useState([]);
  const [postHistroyData,setPostHistroyData] = useState([]);
  const { logout } = useContext(AuthContext);
  useEffect(()=>{ 
      fetchWorkHistroy();
      fetchPostHistroy();
  },[])

  const getworkstyle = (status) => {
  switch (status) {
    case 'completed':
      return { icon: 'check-circle-outline', color1:'#83f28f', color2:'#00ab41' };
    case 'pending':
      return { icon: 'access-time',color1:'#ffe89d',color2:'#ffc60a'};
    case 'cancelled':
      return { icon: 'cancel', color1:'#f46d75',color2:'#ba0001' };
    case 'assigned':
      return { icon : 'assignment-turned-in',color1 : '#007AFF' , color2 : '#fff'}
    default:
      return { icon: 'adjust', color1:'#fff',color2:'#000' };
  }
};

  const getpoststyle = (status) => {
  switch (status) {
    case 'pending':
      return {
        icon: 'hourglass-empty',
        color1: '#E0F7FA', // Light Cyan
        color2: '#00796B'  // Teal (calm and neutral)
      };
    case 'cancelled':
      return {
        icon: 'cancel',
        color1: '#FFEBEE', // Light red background
        color2: '#D32F2F'  // Strong red (warning)
      };
    case 'completed':
      return {
        icon: 'check-circle',
        color1: '#E8F5E9', // Light green
        color2: '#388E3C'  // Success green
      };
    case 'in_progress':
      return {
        icon: 'autorenew',
        color1: '#FFF8E1', // Soft yellow
        color2: '#F9A825'  // Amber (in-progress look)
      };
    default:
      return {
        icon: 'help-outline',
        color1: '#ECEFF1', // Neutral grey
        color2: '#607D8B'  // Blue grey
      };
  }
};
    const fetchWorkHistroy = async ()=>{  
       try {
        const userId = await getUserId();
         const workresponse =await apiRequest('GET',`/api/histroy/users/${userId}/works`,null,logout);
          setWorkHistroyData(workresponse.data.data);
          console.log("w="+workresponse);
        } catch (error) {
          console.error('Error fetching history:', error);
          // Alert.alert('Failed to fetch history', error.message);
        }
    }
    const fetchPostHistroy = async ()=>{ 
        try {
          const userId =await getUserId();
          const postresponse = await apiRequest('GET',`/api/histroy/users/${userId}/posts`,null,logout);
          setPostHistroyData(postresponse.data.data);
          console.log('post histroy : '+postresponse.data.data);
        } catch (error) {
          console.error('Error fetching history:', error);
          Alert.alert('Failed to fetch history', error.message);
        }
    }


  return (
    <SafeAreaView style={{flex: 1,paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,backgroundColor:'#fff'}}>
      <ScrollView showsVerticalScrollIndicator={false} >
        <StatusBar barStyle="dark-content" />
        <Header />
    <View style={styles.container}>
      {/* Tabs */}
  <View style={styles.tabContainer}>
    <Pressable
      style={[styles.tab, activeTab === 'Work History' && styles.activeTab]}
      onPress={()=>setActiveTab('Work History')}
    >
      <Text style={[styles.tabText, activeTab === 'Work History' && styles.activetext]}>Work History</Text>
    </Pressable>
    
    <Pressable
      style={[styles.tab, activeTab === 'Posted History' && styles.activeTab]}
      onPress={()=>setActiveTab('Posted History')}
    >
      <Text style={[styles.tabText, activeTab === 'Posted History' && styles.activetext]}>Posted History</Text>
    </Pressable>
  </View>

  {/* Content */}
  <View style={styles.contentContainer}>
        
    {activeTab === 'Work History' ? (
          workHistroyData.map((item)=>{
            const {icon , color1 , color2} = getworkstyle(item.status)
          return(<Workhistory  workData={item} color1={color1} color2={color2}  icon={icon}  />)
    })
    ) : (
          postHistroyData.map((item)=>{
            const {icon,color1,color2} = getpoststyle(item.status)
            return(<Posthistory key={item.id} color1={color1} color2={color2}  icon={icon}  postDetails={item} />)})
    )}
  </View>
    </View>
  </ScrollView>
  </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  tabContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 8,
  },
  tab: {
    paddingVertical: 8,
    paddingHorizontal: 32,
  },
   activeTab: {
      borderBottomColor:'#000',
      borderBottomWidth:2,
  },
  activetext:{
    color:'#000'
  },
  tabText: {
    fontSize: 16,
    color: '#c1bfbf',
    fontWeight:'500'
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentText: {
    fontSize: 18,
    color: '#333',
  },
});


export default History
