import React, { useContext, useEffect, useState } from 'react'
import { ScrollView, View,Text, SafeAreaView, StatusBar,Platform, Alert, Pressable  } from 'react-native'
import Header from '../components/Header.jsx'
import Locationhead from '../components/Home/Locationhead.jsx'
import Categories from '../components/Categories/Categories.jsx'
import Homeservice from '../components/Home/HomeWorks.jsx'
import { useNavigation } from "@react-navigation/native";
import { apiRequest } from '../../utils/api.js'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { useUserForm } from '../context/UserFormContext.js'
import { AuthContext } from '../context/AuthProvider.js'
import Loading from '../components/Loading.jsx'


const Home = () => {
  const navigation = useNavigation();
   const { setFullFormData ,formData } = useUserForm();
   const [data,setdata] = useState([])
       const { logout } = useContext(AuthContext);

    useEffect(() => {
      const userdetails = async () => {
      const userID = await AsyncStorage.getItem('userId');
      const accessToken =await AsyncStorage.getItem('accessToken');

      try {
        if (!userID) {
        console.error('No userID found in AsyncStorage');
        return;
      }
      else{
        const response = await apiRequest('GET',`/api/users/${userID}`,null,logout);
        console.log(response.data.data[0])
        await setdata(response.data.data[0])
        setFullFormData({
          phone_no: response.data.data[0].phone_no || '',
          email_id: response.data.data[0].email_id || '',
          pushnotification_id: response.data.data[0].pushnotification_id || '',
          role: response.data.data[0].role || 'guest',
          name: response.data.data[0].name || '',
          location_lat: response.data.data[0].location_lat || null,
          location_long: response.data.data[0].location_long || null,
          is_active: response.data.data[0].is_active ?? true,
          is_verified:response.data.data[0].is_verified,
          createdAt: response.data.data[0].createdAt || new Date().toISOString(),
          updatedAt: response.data.data[0].updatedAt || new Date().toISOString(),
        });
      }
      } catch (error) {
        console.error('Error fetching user details:', error);
      }
    };
     userdetails();
      }, []);
 

  return (
   <SafeAreaView style={{flex: 1,paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,backgroundColor:'#fff'}}>
    <ScrollView showsVerticalScrollIndicator={false} >
        <StatusBar barStyle="dark-content" />
        <View>  
            <Header/>
            <Locationhead />
            <Categories navigation={navigation} />
             {
                  formData.location_lat || formData.location_long ? (
                     <Homeservice />

                   ) : (
                    <View style={{flex:1,marginTop:40}} >
                      <Text style={{textAlign:'center'}} >set necessary details to see nearby jobs...</Text>
                      <Pressable onPress={()=>navigation.navigate('updateuser')} ><Text style={{textAlign:'center',color:'blue'}}>Provide details</Text></Pressable>
                    </View>
                   )
                 }
                 
        </View>
      </ScrollView>
   </SafeAreaView>
  )
}

export default Home
