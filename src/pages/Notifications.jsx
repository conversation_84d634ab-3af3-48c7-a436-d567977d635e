import React, { useState, useEffect } from 'react';
import { Platform, Pressable, ScrollView, StatusBar, Text, View } from 'react-native';
import Header from '../components/Header';
import Singlenotification from '../components/Singlenotification';
import { useNavigation } from '@react-navigation/native';
import { apiRequest } from '../../utils/api'
import { getNotificationsCache,storeNotificationCache, getUserId } from '../../utils/storage';
import Loading from '../components/Loading';

const getIconDetails = (status) => {
  switch (status) {
    case 'SUCCESS':
      return { icon: 'check-circle', color: '#4CAF50' }; // Green
    case 'FAILED':
      return { icon: 'cancel', color: '#F44336' }; // Red
    case 'MESSAGE':
    default:
      return { icon: 'message', color: '#2196F3' }; // Blue
  }
};

const Notifications = () => {
  const navigation = useNavigation();
  const [notifications, setNotifications] = useState([]);
  const [visible , setvisible] = useState(false)
 const fetchNotifications = async () => {
  try {
    await setvisible(true)
    const userId = await getUserId();
    const notificationsRes = await apiRequest('GET', `/api/notifications/users/${userId}`, null);
    const newNotifications = notificationsRes.data.data;
    const pastNotifications = await getNotificationsCache();

    // Combine and remove duplicates based on unique 'id' or 'timestamp'
    const combined = [...newNotifications, ...(pastNotifications || [])];

    const uniqueNotifications = Array.from(
      new Map(combined.map(item => [item.id, item])).values()
    );

    await setNotifications(uniqueNotifications);
    await storeNotificationCache(uniqueNotifications);
    await setvisible(false)
  } catch (error) {
    console.error('Error fetching notifications:', error);
  }
};


  useEffect(() => {
    fetchNotifications();
  }, []);

  return (
    <View
      style={{
        flex: 1,
        paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
        backgroundColor: '#fff'
      }}
    >
      <Loading visible={visible} />
      <Header />
      <ScrollView style={{ marginHorizontal: 16 }}>
       {/*  <Pressable onPress={async()=>{await storeNotificationCache(null)}} >
        <View>
          <Text>Click here to clear notifications</Text>
        </View>
        </Pressable> */}
        {notifications?.map((item) => {
          const { icon, color } = getIconDetails(item.status);
          return (
            <Singlenotification
              key={item.id}
              title={item.title}
              description={item.description}
              timestamp={new Date(item.createdAt).toLocaleTimeString()}
              icon={icon}
              iconColor={color}
            />
          );
        })}
      </ScrollView>
    </View>
  );
};

export default Notifications;
