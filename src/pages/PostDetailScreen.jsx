import React, { useEffect, useState, useContext } from 'react';
import {
  View,
  ScrollView,
  Text,
  Alert,
  ActivityIndicator,
  StyleSheet,
  Modal,
  Button,
  Pressable,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { AuthContext } from '../context/AuthProvider';
import { apiRequest } from '../../utils/api';
import ImageCarousel from '../components/PostDetails/ImageCarousel';
import InfoSection from '../components/PostDetails/InfoSection';
import ActionButtons from '../components/PostDetails/ActionButtons';
import PaymentModal from '../components/PostDetails/PaymentModal';


const PostDetailScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { post_id } = route.params;
  const { logout, user } = useContext(AuthContext);

  const [post, setPost] = useState(null);
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [paid, setPaid] = useState(false);
  const [showPayment, setShowPayment] = useState(false);
  const [orderId, setOrderId] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await apiRequest('GET', `/api/posts/${post_id}`, null, logout);
        setPost(response.data.data);
        setImages(response.data.images || []);
        setPaid(response.data.hasPaid || false );
      } catch (error) {
        console.error("Error loading post", error);
        Alert.alert("Error", "Unable to load post.");
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [post_id]);

  const checkpaid = async ()=>{
    try{
      const userId = await AsyncStorage.getItem('userId');
      const checkverify = await apiRequest('GET','/api/report-to-verify',{
        user_id:userId,
        post_id,
      },logout)
      console.log(checkverify.data);
    }catch(error){
      console.log("error in checking already pain",error)
    }
  }
useEffect(()=>{
  checkpaid();
},[])
  const handleConfirm = async () => {
    try {
      const userId = await AsyncStorage.getItem('userId');
      const response = await apiRequest('POST', '/api/payment/create-order', {
        user_id: userId,
        post_id,
      }, logout);

      if (response?.data?.data?.order_id) {
        setOrderId(response.data.data.order_id);
        setShowPayment(true);
      } else {
        Alert.alert("Payment Error", "Unable to create payment order.");
      }
    } catch (e) {
      console.error(e);
      Alert.alert("Error", "Payment failed.");
    }
  };

  const handleAlreadyPaid = async () => {
    try {
      const userId = await AsyncStorage.getItem('userId');
      const verifyResponse = await apiRequest('POST', '/api/payment/verify-payment', {
        user_id: userId,
        post_id,
      }, logout);

      if (verifyResponse?.data?.payment?.status === 'SUCCESS') {
        setPaid(true);
        Alert.alert("Verified", "Payment verified successfully.");
      } else {
        Alert.alert("Not Verified", "Payment not found or incomplete.");
      }
    } catch (err) {
      Alert.alert("Error", "Verification failed.");
      console.error("Verification failed", err);
    }
  };

  const handleCancel = () => {
    navigation.goBack();
  };

  const onPaymentSuccess = async (data) => {
    setShowPayment(false);
    try {
      const userId = await AsyncStorage.getItem('userId');
      const verifyResponse = await apiRequest('POST', '/api/payment/verify-payment', {
        user_id: userId,
        post_id,
      }, logout);

      if (verifyResponse?.data?.payment?.status === 'SUCCESS') {
        setPaid(true);
        Alert.alert("Success", "Payment completed and contact unlocked.");
      } else {
        Alert.alert("Verification Failed", "Payment verification failed.");
      }
    } catch (error) {
      console.error("Verification error", error);
      Alert.alert("Error", "Could not verify payment.");
    }
  };

  const onPaymentMessage = (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      if (data?.razorpay_payment_id) {
        onPaymentSuccess(data);
      } else {
        setShowPayment(false);
        Alert.alert("Cancelled", "You cancelled the payment.");
      }
    } catch (err) {
      setShowPayment(false);
      console.error("WebView parse error", err);
    }
  };

  if (loading) return <ActivityIndicator size="large" style={{ marginTop: 40 }} />;

  return (
    <View style={{ flex: 1 }}>
      <ScrollView style={styles.container}>
        <ImageCarousel images={images} />
        <InfoSection post={post} showContact={paid} />
        <ActionButtons
          paid={paid}
          onConfirm={handleConfirm}
          onCancel={handleCancel}
        />
        {!paid && (
          <View style={styles.alreadyPaidContainer}>
            <Pressable onPress={handleAlreadyPaid} style={styles.verifyButton}>
              <Text style={styles.verifyButtonText}>I Already Paid</Text>
            </Pressable>
          </View>
        )}
      </ScrollView>
     

      {/* Razorpay WebView Modal */}
      <Modal visible={showPayment} animationType="slide">
        <PaymentModal
          visible={showPayment}
          orderId={orderId}
          user={user}
          onClose={() => setShowPayment(false)}
          onMessage={onPaymentMessage}
        />
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#fff',
  },
  alreadyPaidContainer: {
    marginTop: 20,
    alignItems: 'center',
  },
  verifyButton: {
    // backgroundColor: '#4CAF50',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 6,
  },
  verifyButtonText: {
    color: '#000',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default PostDetailScreen;
