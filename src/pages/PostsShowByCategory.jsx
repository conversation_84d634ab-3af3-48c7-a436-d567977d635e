import React, { useEffect, useState, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  Alert,
  StatusBar,
  SafeAreaView,
  Platform,
  Pressable,
  ScrollView,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { apiRequest } from '../../utils/api';
import { AuthContext } from '../context/AuthProvider';

import Singlehomework from '../components/Home/SingleHomeWorkDetail';
import ShowWorkDetails from '../components/Home/HomeworkDetails'; // ✅ Your modal component

import { Feather } from '@expo/vector-icons';
import Header from '../components/Header';
import { useUserForm } from '../context/UserFormContext';

const PostsShowByCategory = () => {
  
  const { logout } = useContext(AuthContext);
  const route = useRoute();

  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);

  const [modalVisible, setModalVisible] = useState(false);
  const [selectedPostData, setSelectedPostData] = useState(null); // ✅ whole object
  const [categoryname , setcategoryname] = useState('')
  const {formData} = useUserForm();
  const navigation = useNavigation();
  const [current , setcurrent] = useState('1');


  const fetchPosts = async () => {
    if(formData.location_lat && formData.location_long){
      try {
      
    const { category_id, category_name } = route.params;
      setcategoryname(category_name)
      const response = await apiRequest('GET', `/api/posts?page=${current}&category_id_eq=${category_id}`, null, logout);
/*       setPosts(response.data.data || []);
 */      setPosts((prev) => {
        const existingIds = new Set(prev.map((post) => post.id));
        const newPosts = response.data.data || [];
        const filteredNewPosts = newPosts.filter((post) => !existingIds.has(post.id));
        return [...prev, ...filteredNewPosts];
      });
    } catch (error) {
      console.error('Failed to fetch posts:', error);
    } finally {
      setLoading(false);
    }
    }else{
      setLoading(false);
      Alert.alert('Set your details', 'Provide your details to see category based data', [
            { text: 'Cancel', style: 'cancel' },
            { text: 'set details', onPress: ()=>navigation.navigate('updateuser'), style: 'destructive' },
          ]);
    }
    
  };
   const loadmorepost = () => {
  const nextPage = (parseInt(current) + 1).toString();
  setcurrent(nextPage);
  fetchPosts(formData.location_lat , formData.location_long);
}

  useEffect(() => {
    fetchPosts();
  }, []);

  const openRequestPanel = (post) => {
    setSelectedPostData(post);
    setModalVisible(true);
    console.log(post)
  };

  const handleRequestJob = async() => {
    console.log('Requesting job for:', selectedPostData?.id);
   
        try {
           const requestForWork =await apiRequest('GET',`/api/works/request`,{
            "post_id":  selectedPostData?.id
           },logout);
           Alert.info(requestForWork || "Request Work Successfully");
          } catch (error) {
            Alert.alert(error.message || "Server Busy, Try Again Later!");
          }
    setModalVisible(false);
  };

  const renderPostItem = ({ item }) => (
    <Singlehomework
      id={item.id}
      category={item.title}
      distance={item.distance || 0}
      description={item.description || 'No description provided'}
      amount={item.amount}
      date={item.job_date}
      onKnowMore={() => openRequestPanel(item)} // ✅ pass full item
    />
  );

  return (
       <SafeAreaView style={{flex: 1,paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,backgroundColor:'#fff'}}>
            <ScrollView showsVerticalScrollIndicator={false} >
    <View  style={{marginBottom:30}} >
       <StatusBar barStyle="dark-content" />
      <Header />
      <View style={styles.header}>
        <Text style={styles.headerText}>Posts by Category {categoryname.replace(/^"(.*)"$/, '$1')} </Text>
      </View>
    

      {loading  ? (
        <ActivityIndicator size="large" color="#2563EB" style={{ marginTop: 20 }} />
      ) : posts.length && formData.location_lat != '' && formData.location_long !='' === 0 ? (
        <Text style={styles.noPosts}>No posts available in this category.</Text>
      ) : (
        /* <FlatList
          data={posts}
          keyExtractor={(item) => item.id}
          renderItem={renderPostItem}
          contentContainerStyle={styles.list}
        /> */
        
         <View>
      {posts.map((item) => (
        <Singlehomework
          key={item.id}
          id={item.id}
          category={item.title}
          distance={item.distance || 0}
          description={item.description || 'No description provided'}
          amount={item.amount}
          date={item.job_date}
          onKnowMore={() => openRequestPanel(item)}
        />
      ))}
    </View>
      )}
     <Pressable  onPress={loadmorepost} >
             <Text style={{textAlign:'center',color:'#2563EB',}} >Load more</Text>
           </Pressable>

      <ShowWorkDetails
        categoryname={categoryname}
        visible={modalVisible}
        data={selectedPostData}
        onCancel={() => setModalVisible(false)}
        onRequest={handleRequestJob}
      />
    </View>
    </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginTop: '10%',
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginTop:10,
    marginBottom: 16,
    marginHorizontal:8
  },
  headerText: {
    fontSize: 20,
    fontWeight: '700',
    color: '#111827',
  },
  list: {
    paddingBottom: 16,
  },
  noPosts: {
    fontSize: 16,
    color: '#EF4444',
    alignSelf: 'center',
    marginTop: 40,
  },
});

export default PostsShowByCategory;
