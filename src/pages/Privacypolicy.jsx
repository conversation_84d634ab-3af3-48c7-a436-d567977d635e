import React from 'react'
import { Button, Platform, Pressable, SafeAreaView, ScrollView, StatusBar, Text, View } from 'react-native'
import { useNavigation } from '@react-navigation/native'

const Privacypolicy = () => {
    const navigation = useNavigation();
  return (
    <SafeAreaView style={{flex: 1,paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,backgroundColor:'#fff'}}>
    <StatusBar barStyle="dark-content" />
    <ScrollView showsVerticalScrollIndicator={false} >
    <View style={{paddingHorizontal:16,paddingTop:25}} >
        <Text style={{fontSize:18,fontWeight:'500',marginBottom:20}} >Privacy Policy</Text>
        <View style={{backgroundColor:'#f5f5f5',padding:20,borderRadius:16}} >
        <Text style={{fontSize:16,fontWeight:'500',marginVertical:6}} >Last updated: April 16, 2025</Text>
        <Text style={{fontSize:15,lineHeight:20}} >Welcome to Daycents! Your privacy is very important to us. This Privacy Policy explains how we collect, use, and protect your personal information when you use the Daycents mobile application.</Text>
        <Text style={{fontSize:16,fontWeight:'500',marginVertical:12}}>1. Information We Collect</Text>
        <Text style={{fontSize:15,lineHeight:20}}>We may collect the following information from you:
Phone Number - used for login and verification purposes.
User Profile Info - such as your name and profile image (if provided).
Location Information - used to show nearby services and to set your preferred service area.
Payment Information - payments are securely processed through third-party providers (e.g., Razorpay or Stripe); we do not store your card or UPI details.
Service Details - if you post a service, we collect details like your description, location, and availability.</Text>
<Text style={{fontSize:16,fontWeight:'500',marginVertical:12}} >2. How We Use Your Information</Text>
<Text style={{fontSize:15,lineHeight:20}}>We use the collected information to:
Help you sign up and log in securely.
Show relevant nearby services based on your location.
Process payments when viewing or posting services.
Display your posted services to other users.
Improve our app and provide better customer support.</Text>
<Text style={{fontSize:16,fontWeight:'500',marginVertical:12}} >3. Data Sharing</Text>
<Text style={{fontSize:15,lineHeight:20}}>We do not sell or rent your personal data to third parties. However, we may share your data with:
Payment providers (Razorpay/Stripe) for transaction processing.
Firebase for OTP authentication and login.
These services follow their own privacy and data protection policies.</Text>
<Text style={{fontSize:16,fontWeight:'500',marginVertical:12}} >4. Data Security</Text>
<Text style={{fontSize:15,lineHeight:20}}>We use industry-standard practices to keep your data secure. All sensitive operations like login and payments are handled over secure connections. However, no method of transmission over the Internet is 100% secure, and we cannot guarantee absolute security.</Text>
<Text style={{fontSize:16,fontWeight:'500',marginVertical:12}} >5. Your Choices</Text>
<Text style={{fontSize:15,lineHeight:20}}>You can update or delete your profile information at any time.
You may uninstall the app to stop sharing location data.
If you'd like to delete your account or data, contact <NAME_EMAIL>.
</Text>

<Text style={{fontSize:16,fontWeight:'500',marginVertical:12}} >6. Changes to This Policy</Text>
<Text style={{fontSize:15,lineHeight:20}}>We may update this Privacy Policy from time to time. If we make changes, we will notify you via the app or by other means.</Text>
<Text style={{fontSize:16,fontWeight:'500',marginVertical:12}} >7. Contact Us</Text>
<Text style={{fontSize:15,lineHeight:20}}>If you have any questions or concerns about this policy, please reach out to us at:
<EMAIL></Text>
</View>
    </View>
    </ScrollView>
    <Pressable style={{marginVertical:12}} onPress={()=>navigation.navigate('Profile')} >
        <View style={{display:'flex',flexDirection:'row',backgroundColor:'#000',alignItems:'center',justifyContent:'center',paddingVertical:12,marginHorizontal:16,borderRadius:10}} >
        <Text style={{color:'#fff',fontSize:16,fontWeight:'600'}} >Continue</Text>
        </View>
    </Pressable>
    </SafeAreaView>
  )
}
export default Privacypolicy;
