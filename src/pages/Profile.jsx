// ProfileScreen.js
import React, { useContext, useEffect, useState, useCallback } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  StatusBar,
  Alert,
  SafeAreaView,
  Platform,
  Pressable,
  Text,
  Modal,
} from 'react-native';
import LocationPopup from '../components/Home/LocationPopup';
import Header from '../components/Header';
import UserCard from '../components/Profile/UserCard';
import ProfileLinks from '../components/Profile/ProfileLinks';
import SettingsToggles from '../components/Profile/SettingsToggles';
import UpdateProfileModal from '../components/Profile/UpdateProfileModal';
import LocationModal from '../components/LocationModal';
import QuickLinks from '../components/Profile/QuickLinks';

import { AuthContext } from '../context/AuthProvider';
import { useUserForm } from '../context/UserFormContext';
import { getLocationName } from '../../utils/geocode';
import { UserFormContext } from '../context/UserFormContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import Userform from '../../auth/Userform';
import { getSearchLocationCache, setUserId } from '../../utils/storage';
import Feather from '@expo/vector-icons/Feather';

const ProfileScreen = () => {
  const { logout } = useContext(AuthContext);
  const { formData,setFullFormData } = useUserForm();
  const navigation = useNavigation();
  const [locationName, setLocationName] = useState('');
  const [isLocationEnabled, setIsLocationEnabled] = useState(true);
  const [isPushEnabled, setIsPushEnabled] = useState(true);
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [visible , setvisible] = useState(false)
  

  const { name, email_id, phone_no, location_lat, location_long } = formData;

  // Fetch human-readable location from coordinates
  useEffect(() => {
    
    const fetchLocationName = async () => {
      const location = await getSearchLocationCache();
      if(location){
      setLocationName(location);
      return;

      }
      try {
        if (location_lat && location_long) {
          const resolvedName = await getLocationName(location_lat, location_long);
          setLocationName(resolvedName);
        }
      } catch (err) {
        console.warn('Failed to fetch location name', err);
      }
    };

    fetchLocationName();
  }, [location_lat, location_long]);
  const logoutuser = async ()=>{
    await AsyncStorage.removeItem('accessToken');
      await AsyncStorage.removeItem('refreshToken');
      await AsyncStorage.removeItem('userId');
      await AsyncStorage.removeItem('search_coordinates');
      setFullFormData({});
      logout();
  }

  // Logout confirmation
  const handleLogout = useCallback(() => {
    Alert.alert('Logout', 'Are you sure you want to logout?', [
      { text: 'Cancel', style: 'cancel' },
      { text: 'Logout', onPress: logoutuser, style: 'destructive' },
    ]);
  }, [logout]);
  
  const handleSelect = async(item) => {
    const { formatted } = item.properties;
    const [lon, lat] = item
    .geometry.coordinates;
  
    // console.log('Selected:', formatted);
    // console.log('Lat:', lat, 'Lon:', lon);
    setLocationName(formatted)
    console.log("HANDLE SELECT"+formatted)
  
    await AsyncStorage.setItem('location',formatted);
    await AsyncStorage.setItem('user_coords',JSON.stringify({
      latitude:lat,
      longitude:lon
    }));
  }

  // Handlers for modals
  const handleEditPress = () =>  {navigation.navigate('updateuser')} /* setShowUpdateModal(true) */;
  const handleLocationPress = () => setShowLocationModal(true);
  const closeUpdateModal = () => setShowUpdateModal(false);
  const closeLocationModal = () => setShowLocationModal(false);
  const user={
    id: 'd4f5e6a7-89b2-4c3e-9d55-8a1b4c6e7a9f',
    phone: '9876543210',
    email: '<EMAIL>',
    password: 'hashed_password_sample',
    pushnotification_id: 'expo_push_token[example123456]',
    role: 'worker', // or 'guest'
    name: 'Ramesh Kumar',
    location_lat: 12.971599,
    location_long: 77.594566,
    is_active: true,
    is_verified: true
  };

  return (
    <SafeAreaView style={{flex: 1,paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,backgroundColor:'#fff'}}>
    <ScrollView style={styles.container}>
      <StatusBar backgroundColor="#fff" barStyle="dark-content" />
      <Header title="Profile" />

      <UserCard
        user={formData}
        locationName={locationName}
        onEditPress={handleEditPress}
        onLocationPress={handleLocationPress}
      />
      <QuickLinks/>

      <Pressable onPress={()=>setvisible(true)} style={styles.linkItem} >
                  <Feather name="phone" size={20} color="black" />
                  <Text style={styles.label} allowFontScaling={false}>Contact us</Text>
                </Pressable>

      {/* <SettingsToggles
        isLocationEnabled={isLocationEnabled}
        setIsLocationEnabled={setIsLocationEnabled}
        isPushEnabled={isPushEnabled}
        setIsPushEnabled={setIsPushEnabled}
      /> */}

      <ProfileLinks onLogout={handleLogout} />
      


      {/* Modals */}
      {/* <UpdateProfileModal
        user={{
          id: 'd4f5e6a7-89b2-4c3e-9d55-8a1b4c6e7a9f',
          phone_no: '9876543210',
          email_id: '<EMAIL>',
          password: 'hashed_password_sample',
          pushnotification_id: 'expo_push_token[example123456]',
          role: 'worker', // or 'guest'
          name: 'Ramesh Kumar',
          location_lat: 12.971599,
          location_long: 77.594566,
          is_active: true,
          is_verified: true
        }}
        visible={showUpdateModal}
        onClose={closeUpdateModal}
      /> */}
       <Modal visible={visible} animationType="slide" transparent>
            <View style={styles.overlay}>
              <View style={styles.popup}>
               <Text style={{fontSize:20,fontWeight:'600',textAlign:'center',paddingBottom:16}} >Contact Us</Text>
               <View style={{display:'flex',flexDirection:'row',gap:10}} ><Feather name="mail" size={24} color="black" /><Text allowFontScaling={false} style={{fontSize:15,fontWeight:'400',textAlign:'center',paddingBottom:16}}><EMAIL></Text></View>
               <View style={{display:'flex',flexDirection:'row',gap:10}} ><Feather name="phone" size={24} color="black" /><Text allowFontScaling={false} style={{fontSize:15,fontWeight:'400',textAlign:'center',paddingBottom:16}}>+91 7397112871</Text></View>
               <View style={{display:'flex',flexDirection:'row',gap:10}} ><Feather name="phone" size={24} color="black" /><Text allowFontScaling={false} style={{fontSize:15,fontWeight:'400',textAlign:'center',paddingBottom:16}}>+91 8900948785</Text></View>
              <Pressable onPress={()=>setvisible(false)} ><Text allowFontScaling={false} style={{color:'#000',fontSize:15,fontWeight:'400',textAlign:'center',paddingVertical:10}} >close</Text></Pressable>
              </View>
            </View>
          </Modal>

      <LocationPopup
        visible={showLocationModal}
        onSelect={handleSelect}
        onClose={() => setShowLocationModal(false)}
      />
    </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  linkItem: {
    marginHorizontal:20,
    width: '100%',
    gap:12,
    display:'flex',
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom:10,
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
  },
  label: {
    fontSize: 16,
    color: '#333',
    flexShrink: 1,
    flex: 1,
    flexWrap: 'nowrap', 
  },
   overlay: {
    flex: 1,
    backgroundColor: '#00000099',
    justifyContent: 'center',
    alignItems: 'center',
  },
  popup: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    elevation: 5,
  },
});

export default ProfileScreen;
