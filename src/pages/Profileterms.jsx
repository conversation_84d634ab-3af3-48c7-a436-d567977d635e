import React from 'react'
import { Button, Platform, Pressable, SafeAreaView, ScrollView, StatusBar, Text, View } from 'react-native'
import { useNavigation } from '@react-navigation/native'

const Profileterms = () => {
    const navigation = useNavigation();
  return (
    <SafeAreaView style={{flex: 1,paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,backgroundColor:'#fff'}}>
    <StatusBar barStyle="dark-content" />
    <ScrollView showsVerticalScrollIndicator={false} >
    <View style={{paddingHorizontal:16,paddingTop:25}} >
        <Text style={{fontSize:18,fontWeight:'500',marginBottom:20}} >Terms & Conditons</Text>
        <View style={{backgroundColor:'#f5f5f5',padding:20,borderRadius:16}} >
        <Text style={{fontSize:16,fontWeight:'500',marginBottom:12}} >Worker Terms & Conditions</Text>
        <Text style={{fontSize:15,lineHeight:20}} >You can send a request to the job post.
Once the owner accepts your request, you must pay ₹10 to view their contact details.
If you don't show up after confirmation, your account will be banned for 2 days.
Repeated no-shows may lead to permanent suspension.</Text>
        <Text style={{fontSize:16,fontWeight:'500',marginBottom:12,marginTop:12}}>Owner Terms & Conditions</Text>
        <Text style={{fontSize:15,lineHeight:20}}>You can accept or reject any worker who sends a request.
If you cancel the job after assigning it to a worker, a ₹50 penalty will be added to your account.
If you don't pay the worker after job completion, legal action may be taken, and your account will be permanently blocked.</Text>
</View>
    </View>
    </ScrollView>
    <Pressable style={{marginBottom:12}} onPress={()=>navigation.navigate('Profile')} >
        <View style={{display:'flex',flexDirection:'row',backgroundColor:'#000',alignItems:'center',justifyContent:'center',paddingVertical:12,marginHorizontal:16,borderRadius:10}} >
        <Text style={{color:'#fff',fontSize:16,fontWeight:'600'}} >Continue</Text>
        </View>
    </Pressable>
    </SafeAreaView>
  )
}

export default Profileterms
