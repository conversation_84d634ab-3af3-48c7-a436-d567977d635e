import React from 'react'
import { Platform, SafeAreaView, ScrollView, StatusBar, StyleSheet, Text, TextInput, View } from 'react-native'
import { AntDesign, MaterialCommunityIcons } from '@expo/vector-icons'

const Search = () => {
  return (
    <SafeAreaView style={{flex: 1,paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,backgroundColor:'#fff'}}>
    <ScrollView showsVerticalScrollIndicator={false} >
    <StatusBar barStyle="dark-content" />
    <View style={styles.constiner} >
    <View style={styles.search} >
    <AntDesign name="search1" size={20} color="black" />
    <TextInput autoFocus={true} style={{width:'100%'}} placeholder='Search' />
    </View>
    </View>
    </ScrollView>
</SafeAreaView>  )
}

const styles = StyleSheet.create({
  constiner:{
    marginHorizontal:16,
    marginTop:10
  },
  search:{
    height:40,
    display:'flex',
    flexDirection:'row',
    alignItems:'center',
    justifyContent:'space-between',
    borderRadius:8,
    paddingLeft:10,
    backgroundColor:'#e0e8f2',
    gap:10,
  }
})

export default Search
