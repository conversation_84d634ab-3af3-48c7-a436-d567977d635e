import { GEOAPIFY_API_KEY } from '@env'; // Make sure your key is in the .env file

export const getLocationName = async (latitude, longitude) => {
  try {
    const url = `https://api.geoapify.com/v1/geocode/reverse?lat=${latitude}&lon=${longitude}&apiKey=${GEOAPIFY_API_KEY}`;

    const response = await fetch(url);
    const data = await response.json();

    if (
      data &&
      data.features &&
      data.features.length > 0 &&
      data.features[0].properties
    ) {
      const location = data.features[0].properties;

      // You can return city, formatted address, or any property you prefer
      return location.formatted;
    } else {
      return 'Unknown Location';
    }
  } catch (error) {
    console.error('Error fetching location:', error);
    return 'Unknown Location';
  }
};
