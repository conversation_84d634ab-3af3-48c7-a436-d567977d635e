import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

// ✅ Your backend base URL
const baseURL = 'https://api-daycents-backend.vercel.app/';

// ✅ Helper function to call API with token and auto-refresh on 401
export const multipart = async (method, endpoint, data = {}, logout) => {
  try {
    const accessToken = await AsyncStorage.getItem('accessToken');
    const refreshToken = await AsyncStorage.getItem('refreshToken');

    // First attempt with existing access token
    const response = await axios({
      method,
      url: `${baseURL}${endpoint}`,
      data,
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'multipart/form-data; boundary=---011000010111000001101001',
      },
    });

    return response;
  } catch (error) {
    const originalRequest = {
      method,
      url: `${baseURL}${endpoint}`,
      data,
    };

    // Handle 401 error
    if (error.status === 401) {
      try {
        const refreshToken = await AsyncStorage.getItem('refreshToken');

        // Use plain Axios instance to avoid loops
        const refreshRes = await axios.post(
          `${baseURL}/api/users/auth/refresh`,
          { refresh_token : refreshToken },
          {
            headers: { 'Content-Type': 'application/json' },
          }
        );

        const newAccessToken = refreshRes.data.token.access_token;
        await AsyncStorage.setItem('accessToken', newAccessToken);

        // Retry original request with new token
        const retryRes = await axios({
          ...originalRequest,
          headers: {
            'Authorization': `Bearer ${newAccessToken}`,
            'Content-Type': 'multipart/form-data; boundary=---011000010111000001101001',
        },
        });

        return retryRes;
      } catch (refreshError) {
        console.log('🔴 Refresh token failed:', refreshError.response?.data || refreshError.message);
        logout(); // You must define this to clear storage and navigate to login
        throw refreshError;
      }
    }

    // Any other error
    throw error;
  }
};