import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Alert } from 'react-native';

export async function registerForPushNotificationsAsync() {
  if (!Device.isDevice) {
    Alert.alert('Push Notifications', 'Must use physical device for Push Notifications');
    return null;
  }

  const { status: existingStatus } = await Notifications.getPermissionsAsync();
  let finalStatus = existingStatus;

  if (existingStatus !== 'granted') {
    const { status } = await Notifications.requestPermissionsAsync();
    finalStatus = status;
  }

  if (finalStatus !== 'granted') {
    Alert.alert('Push Notifications', 'Permission not granted!');
    return null;
  }

  const tokenData = await Notifications.getExpoPushTokenAsync();
  return tokenData.data;
}





import AsyncStorage from '@react-native-async-storage/async-storage';

// Notification listener
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true, // shows banner while app is in foreground
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

Notifications.addNotificationReceivedListener(async (notification) => {
  console.log("NOTIFICATION CATCHED")
  const newNotification = {
    id: Date.now().toString(),
    title: notification.request.content.title,
    body: notification.request.content.body,
    data: notification.request.content.data,
    receivedAt: new Date().toISOString(),
  };

  try {
    const existing = await AsyncStorage.getItem('notifications');
    const notifications = existing ? JSON.parse(existing) : [];
    notifications.unshift(newNotification); // Add to top
    await AsyncStorage.setItem('notifications', JSON.stringify(notifications));
  } catch (err) {
    console.error('Error saving notification:', err);
  }
});
