import AsyncStorage from "@react-native-async-storage/async-storage";

export const setLogin = async () => {
  await AsyncStorage.setItem("isLoggedIn", "true");
};

export const logout = async () => {
  await AsyncStorage.removeItem("isLoggedIn");
};

export const setUserId = async (userId) => {
  return await AsyncStorage.setItem("userId",userId);
};
export const getUserId = async () => {
  return await AsyncStorage.getItem("userId");
};


export async function getNotificationsCache() {
  try {
    const data = await AsyncStorage.getItem('notifications');
    return data ? JSON.parse(data) : [];
  } catch (err) {
    console.error('Error retrieving notifications:', err);
    return [];
  }
}
export const storeNotificationCache = async (userData) => {
  return await AsyncStorage.setItem('notifications', JSON.stringify(userData));
};
export const clearNotificationCache = async () => {
  await AsyncStorage.removeItem("notifications");
};

export const storelocation = async (location) => {
  return await AsyncStorage.setItem('location', location);
};

export const getlocation = async () => {
  return await AsyncStorage.getItem('location');
};




export const storeUserDataCache = async (userData) => {
  return await AsyncStorage.setItem('categories_cache', JSON.stringify(userData));
};
export async function getUserDataCache() {
  try {
    const data = await AsyncStorage.getItem('user_data');
    return data ? JSON.parse(data) : [];
  } catch (err) {
    console.error('Error in Storage.js:', err);
    return [];
  }
}
export async function getCategoriesCache() {
  try {
    const raw = await AsyncStorage.getItem('categories_cache');
    if (!raw) return null;

    const { data, timestamp } = JSON.parse(raw);

    const now = Date.now();
    const twentyFourHours = 24 * 60 * 60 * 1000;

    if (now - timestamp > twentyFourHours) {
      // Cache expired
      await AsyncStorage.removeItem('categories_cache');
      return null;
    }

    return data;
  } catch (err) {
    console.error('Error retrieving categories cache:', err);
    return null;
  }
}


export const storeCategoriesCache = async (categoriesData) => {
  try {
    const cacheObject = {
      data: categoriesData,
      timestamp: Date.now(), // current time in milliseconds
    };
    await AsyncStorage.setItem('categories_cache', JSON.stringify(cacheObject));
  } catch (err) {
    console.error('Error storing categories cache:', err);
  }
};
// lat,long,name
export async function getSearchLocationCache() {
  try {
    const data = await AsyncStorage.getItem('search_coordinates');
    return data ? JSON.parse(data) : [];
  } catch (err) {
    console.error('Error retrieving notifications:', err);
    return [];
  }
}

export const storeSearchLocationCache = async (SearchLocationData) => {
  return await AsyncStorage.setItem('search_coordinates', JSON.stringify(SearchLocationData));
};

export const removeLocationcache = async ()=>{
  return await AsyncStorage.removeItem('search_coordinates');
}

